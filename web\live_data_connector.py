#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据连接器
==============

连接到正在运行的Estia记忆系统，获取实时监控数据
"""

import os
import sys
import sqlite3
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

class LiveDataConnector:
    """实时数据连接器，从正在运行的Estia系统获取数据"""
    
    def __init__(self):
        self.db_path = "assets/memory.db"
        self.vector_path = "data/vectors/memory_index.bin"
        self.cache_path = "cache"
        
    def check_system_running(self) -> bool:
        """检查Estia系统是否正在运行"""
        try:
            # 检查数据库文件是否存在且可访问
            if not os.path.exists(self.db_path):
                return False
            
            # 尝试连接数据库
            conn = sqlite3.connect(self.db_path, timeout=1.0)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM memories")
            conn.close()
            return True
            
        except Exception as e:
            print(f"系统检查失败: {e}")
            return False
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            cursor = conn.cursor()
            
            # 获取记忆总数
            cursor.execute("SELECT COUNT(*) FROM memories")
            total_memories = cursor.fetchone()[0]
            
            # 获取最近的记忆
            cursor.execute("""
                SELECT created_at, weight, content 
                FROM memories 
                ORDER BY created_at DESC 
                LIMIT 10
            """)
            recent_memories = cursor.fetchall()
            
            # 获取权重分布
            cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN weight >= 8.0 THEN 1 END) as high_weight,
                    COUNT(CASE WHEN weight >= 5.0 AND weight < 8.0 THEN 1 END) as medium_weight,
                    COUNT(CASE WHEN weight < 5.0 THEN 1 END) as low_weight
                FROM memories
            """)
            weight_dist = cursor.fetchone()
            
            # 获取今天创建的记忆数量
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("""
                SELECT COUNT(*) FROM memories 
                WHERE DATE(created_at) = ?
            """, (today,))
            today_memories = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_memories': total_memories,
                'today_memories': today_memories,
                'recent_memories': [
                    {
                        'created_at': mem[0],
                        'weight': mem[1],
                        'content': mem[2][:100] + '...' if len(mem[2]) > 100 else mem[2]
                    }
                    for mem in recent_memories
                ],
                'weight_distribution': {
                    'high_weight': weight_dist[0],
                    'medium_weight': weight_dist[1], 
                    'low_weight': weight_dist[2]
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"获取记忆统计失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            cursor = conn.cursor()
            
            # 获取会话相关的记忆
            cursor.execute("""
                SELECT created_at, metadata 
                FROM memories 
                WHERE metadata LIKE '%session%' 
                ORDER BY created_at DESC 
                LIMIT 20
            """)
            session_memories = cursor.fetchall()
            
            # 解析会话信息
            sessions = []
            session_ids = set()
            
            for mem in session_memories:
                try:
                    if mem[1]:  # metadata不为空
                        metadata = json.loads(mem[1])
                        if 'session_id' in metadata:
                            session_id = metadata['session_id']
                            if session_id not in session_ids:
                                sessions.append({
                                    'session_id': session_id,
                                    'created_at': mem[0],
                                    'metadata': metadata
                                })
                                session_ids.add(session_id)
                except json.JSONDecodeError:
                    continue
            
            # 获取最近的对话记录
            cursor.execute("""
                SELECT content, created_at, weight 
                FROM memories 
                WHERE content LIKE '%用户:%' OR content LIKE '%AI:%'
                ORDER BY created_at DESC 
                LIMIT 10
            """)
            recent_dialogues = cursor.fetchall()
            
            conn.close()
            
            return {
                'total_sessions': len(sessions),
                'recent_sessions': sessions[:5],  # 最近5个会话
                'recent_dialogues': [
                    {
                        'content': dialogue[0][:200] + '...' if len(dialogue[0]) > 200 else dialogue[0],
                        'created_at': dialogue[1],
                        'weight': dialogue[2]
                    }
                    for dialogue in recent_dialogues
                ],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"获取会话统计失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            health_info = {
                'database_accessible': False,
                'vector_index_exists': False,
                'cache_directory_exists': False,
                'last_activity': None,
                'system_status': 'unknown'
            }
            
            # 检查数据库
            if os.path.exists(self.db_path):
                try:
                    conn = sqlite3.connect(self.db_path, timeout=1.0)
                    cursor = conn.cursor()
                    cursor.execute("SELECT MAX(created_at) FROM memories")
                    last_activity = cursor.fetchone()[0]
                    conn.close()
                    
                    health_info['database_accessible'] = True
                    health_info['last_activity'] = last_activity
                except:
                    pass
            
            # 检查向量索引
            health_info['vector_index_exists'] = os.path.exists(self.vector_path)
            
            # 检查缓存目录
            health_info['cache_directory_exists'] = os.path.exists(self.cache_path)
            
            # 判断系统状态
            if health_info['database_accessible']:
                if health_info['last_activity']:
                    last_time = datetime.fromisoformat(health_info['last_activity'].replace('Z', '+00:00'))
                    time_diff = datetime.now() - last_time.replace(tzinfo=None)
                    
                    if time_diff.total_seconds() < 300:  # 5分钟内有活动
                        health_info['system_status'] = 'active'
                    elif time_diff.total_seconds() < 3600:  # 1小时内有活动
                        health_info['system_status'] = 'idle'
                    else:
                        health_info['system_status'] = 'inactive'
                else:
                    health_info['system_status'] = 'no_data'
            else:
                health_info['system_status'] = 'offline'
            
            health_info['timestamp'] = datetime.now().isoformat()
            return health_info
            
        except Exception as e:
            return {
                'error': str(e),
                'system_status': 'error',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_comprehensive_data(self) -> Dict[str, Any]:
        """获取综合数据"""
        return {
            'system_running': self.check_system_running(),
            'memory_stats': self.get_memory_statistics(),
            'session_stats': self.get_session_statistics(),
            'system_health': self.get_system_health(),
            'timestamp': datetime.now().isoformat()
        }


# 全局实例
live_connector = LiveDataConnector()
