---
description: 
globs: 
alwaysApply: false
---
# 记忆系统架构指南

## 记忆系统核心
记忆系统位于 `core/memory/` 目录，采用模块化设计。

## 核心组件

### 管道系统
- [core/memory/pipeline.py](mdc:core/memory/pipeline.py) - 记忆处理主管道

### 初始化模块
- [core/memory/init/db_manager.py](mdc:core/memory/init/db_manager.py) - 数据库管理
- [core/memory/init/vector_index.py](mdc:core/memory/init/vector_index.py) - 向量索引初始化

### 存储层
- [core/memory/storage/memory_store.py](mdc:core/memory/storage/memory_store.py) - 记忆存储管理

### 嵌入向量
- [core/memory/embedding/vectorizer.py](mdc:core/memory/embedding/vectorizer.py) - 向量化处理
- [core/memory/embedding/cache.py](mdc:core/memory/embedding/cache.py) - 嵌入缓存

### 检索系统
- [core/memory/retrieval/faiss_search.py](mdc:core/memory/retrieval/faiss_search.py) - FAISS向量检索

### 关联网络
- [core/memory/association/network.py](mdc:core/memory/association/network.py) - 记忆关联网络

### 上下文管理
- [core/memory/context/history.py](mdc:core/memory/context/history.py) - 历史上下文管理

### 排序系统
- [core/memory/ranking/scorer.py](mdc:core/memory/ranking/scorer.py) - 记忆评分排序

## 设计原则
1. **分层架构** - 不同类型记忆分层管理
2. **向量检索** - 基于语义相似度的快速检索
3. **缓存优化** - 多级缓存提升性能
4. **关联学习** - 记忆间的关联关系建模


