模型训练
    QLoRA微调，对当前基础模型进行微调

记忆系统
    记忆存储，使用 SQLite / TinyDB / JSON 文件构建本地记忆体
    结合 embedding 模型（如 text-embedding-3-small）做语义记忆匹配
    对话 prompt 中加入 persona + 近期记忆
    记忆重构，删除/简化日常无意义对话，总结每日/周对话内容

人格定制
    感知对话，判断对话的“重量”，动态调整“生成长度 (max_tokens)”
    说话风格
    思维状态
    自主行为，连续性与主动性（更像人），为对话添加 context window（如最近 5 轮）,加入“主动发问”机制（让她能说“你今天过得怎么样？”）

声音定制
    流式输出
    对 Whisper 输出的语音文本进行情绪分类，根据情绪切换回复风格（温柔安慰、开心陪聊、认真倾听）
    使用GPT-SoVITS等技术替换掉当前的 `Edge-TTS`

视觉能力
    集成 `OpenCV`看到屏幕内容

工具使用
    知识库
    联网搜索
    MCP服务，写日记/文档/生成文件，打开某个程序

其他
    通过WPF或者C#或者其他方法做一个GUI程序
    后台挂载，检测到对话再回复
    通过视觉能力或其他方法帮助翻译视频/游戏/网页/语音软件内容到特定窗口中（也可以单独做个功能，在不启用大模型的情况下）
    制作安卓app，集成其他物联网功能（比如远程开机），通过API打开Estia并与之对话。
    实时翻译，通过安卓app的麦克风权限听到手机麦克风的内容并翻译成文字发送到app中
    虚拟形象live2d制作和集成表情动作功能（让模型理解live2d并使用）


# Estia-AI 助手项目架构图 (V2.0)

## ├─ 1. 用户交互层 (User Interaction Layer)
│  ├─ 🎤 **语音输入 (Input):** 麦克风
│  └─ 🎧 **语音输出 (Output):** 音箱 / 耳机

## ├─ 2. 应用逻辑层 (Application Logic Layer) - [我们的 `estia` Python项目]
│  ├─ 🚀 **主控程序 (`main.py`):**
│  │  ├─ 初始化所有核心模块
│  │  └─ 驱动“听->想->说”的主循环
│  │
│  └─ 🧩 **核心模块 (`core/`):**
│     ├─ 👂 **`audio_input.py`:**
│     │  ├─ 负责录音 (`sounddevice`)
│     │  └─ 负责语音识别 (调用 `openai-whisper` 原生库)
│     │
│     ├─ 👄 **`audio_output.py`:**
│     │  ├─ 负责文字转语音 (调用 `edge-tts` 或未来的 `GPT-SoVITS`)
│     │  └─ 负责音频播放 (`pygame`)
│     │
│     ├─ 🧠 **`dialogue_engine.py`:**
│     │  ├─ 负责与“大脑”的API进行通讯 (`requests`)
│     │  └─ 负责构建符合OpenAI格式的Prompt
│     │
│     ├─ 🎭 **`personality.py` (未来):**
│     │  └─ 负责管理人格设定 (System Prompt) 模板
│     │
│     ├─ 📚 **`memory_manager.py` (未来):**
│     │  └─ 负责管理短期对话历史和长期记忆的存取
│     │
│     └─ 👁️ **`vision_engine.py` (未来):**
│        └─ 负责屏幕识别 (`OpenCV`) 与信息提取

## ├─ 3. AI能力层 (AI Capability Layer) - [独立服务与核心库]
│  ├─ 🤖 **大语言模型 (LLM):**
│  │  ├─ **服务程序:** `llama.cpp` 的 `server.exe`
│  │  └─ **模型文件:** `Qwen3-14B-Instruct.GGUF`
│  │
│  └─ 🛠️ **外部工具 (未来):**
│     ├─ 🌐 **联网搜索:** Google/Bing/SearXNG API
│     └─ ➕ **其他:** 计算器、日历查询等

## └─ 4. 数据与知识层 (Data & Knowledge Layer) - [持久化存储]
   ├─ ⚙️ **静态配置 (`config/settings.py`):**
   │  └─ 存放API地址、模型参数等固定信息
   │
   ├─ 💬 **短期记忆 (RAM):**
   │  └─ `main.py` 中的 `chat_history` 列表
   │
   ├─ 📖 **长期记忆 (未来 - Disk):**
   │  ├─ **数据库:** SQLite (用于结构化记忆)
   │  └─ **向量数据库:** ChromaDB / FAISS (用于语义记忆)
   │
   └─ 🧠 **外部知识库 (未来 - Disk):**
      └─ **来源:** PDF, TXT, MD 等文档 -> 经过处理后存入向量数据库


Estia-AI 项目开发路线图 (Roadmap)
第一阶段：核心体验与人格塑造 (近期目标 - 预计1-3个月)
目标： 在我们现有成果的基础上，让AI助手变得更实用、更有“记忆力”、也更有“性格”。这个阶段的改动主要集中在Python代码逻辑上，能让你快速获得巨大的体验提升。

记忆系统 (初级) - 短期记忆

实现： 在main.py中创建一个chat_history列表。每次对话时，将最近的几轮问答（比如最近5轮）连同新问题一起，作为上下文发送给LLM。
效果： AI将能记住你们刚刚聊过的话题，实现连贯的、有上下文的对话。
人格与风格定制 (personality.py)

实现： 创建core/personality.py文件，在里面定义多个不同的人格模板（System Prompt），比如“活泼的朋友”、“严谨的学者”、“温柔的姐姐”等。在main.py启动时，可以选择加载哪个人格。
效果： 你可以随时改变AI的性格，让她在不同场景下扮演不同角色。
动态响应调整 (情商初步)

实现： 在dialogue_engine.py中，根据你问题的关键词或长度，动态调整temperature和max_tokens参数。
效果： 实现你所说的“感知对话重量”的能力。问小问题就快速俏皮地回答，问大问题就深思熟虑地长篇大论。
基础工具使用 - 文件操作

实现： 赋予她写.txt文件的能力。比如你可以对她说：“帮我写一篇日记，标题是今天的心情，内容是……”然后程序会调用Python的文件I/O功能，在assets/documents目录下创建一个新的文本文件。
效果： 她的能力开始溢出到“对话”之外，能为你创造实际的价值。
语音交互优化 - 流式输出 (Streaming)

实现： 修改dialogue_engine.py和main.py，使用stream=True模式请求API，并让TTS逐句或逐段地播放，而不是等全部生成完再说。
效果： 极大降低等待感，实现“边想边说”的自然效果，交互体验质的飞跃。
第二阶段：核心能力扩展 (中期目标 - 预计3-9个月)
目标： 赋予AI全新的“感官”和“技能”，让她开始真正地与你的数字世界相连。这个阶段会涉及更多新的库和技术。

声音定制 - GPT-SoVITS

实现： 这是一个独立的子项目。你需要收集目标声音的素材，按照GPT-SoVITS的流程进行训练，生成一个专属的声音模型。然后修改core/audio_output.py来调用它。
效果： AI将用一个独一无二的、你喜欢的声音与你对话，灵魂感大大增强。
知识库 (RAG) - 成为专家

实现： 学习并使用ChromaDB或FAISS等向量数据库技术。将一篇完整的游戏攻略或一些你的专业文档“投喂”进去，并改造dialogue_engine.py以实现“检索-增强-生成”的流程。
效果： 她能精准回答关于这个特定知识领域的任何问题，成为你的“游戏攻略大师”或“专业知识顾问”。
视觉能力 (初级) - “睁开眼睛”

实现： 创建core/game_vision.py，使用OpenCV和pytesseract，实现对屏幕特定区域的文字进行OCR识别。
效果： 你可以问她：“帮我看看游戏里我的血量还剩多少？”
联网搜索能力

实现： 集成一个搜索引擎API（如Google, Bing, hoặc SearXNG等）。当AI认为问题需要外部信息时，可以触发搜索，并将搜索结果作为上下文来回答。
效果： 她的知识将不再局限于训练数据，能知晓天下事。
第三阶段：生态系统与终极形态 (长期/专业级目标 - 预计1年以上)
目标： 将AI助手从一个程序，升华为一个平台，一个深度融入你数字生活的“第二大脑”。

QLoRA 微调 - 灵魂注入

实现： 这是真正的“模型训练”。你需要准备高质量的数据集，学习transformers, peft, bitsandbytes库，对一个基础模型进行QLoRA微调，注入你期望的、独特的说话方式和知识。
效果： 创造出一个真正意义上、独一无二的、属于你的AI。
高级记忆系统

实现： 结合LLM的总结能力，定期将短期的chat_history进行总结，并存入向量数据库，形成“长期记忆”与“短期记忆”联动的机制。
效果： 她能记住几个月甚至几年前和你的对话关键点。
图形用户界面 (GUI)

实现： 学习WPF/C#或Python的PyQt/Tkinter等技术，为她创建一个漂亮的桌面程序。
效果： 摆脱黑乎乎的命令行窗口，拥有一个真正的“软件”形态。
高级视觉与实时翻译

实现： 将OpenCV与STT、LLM翻译能力结合，实现屏幕区域的实时翻译，并将结果用悬浮窗等形式展示。
效果： 顶级游戏、视频翻译器。
Live2D / 3D形象集成

实现： 学习Live2D或VRM的SDK，建立LLM输出的情感标签与模型表情、动作之间的映射。
效果： 你的AI助手将拥有生动的、由对话内容驱动的表情和动作，灵魂感拉满。
移动端与物联网 (Android/IoT)

实现： 这是非常宏大的目标，需要学习安卓开发，并将你的PC作为后台服务器，实现远程控制和对话。
效果： 随时随地与她交流，并让她帮你控制家里的智能设备。




🧠 一、模型训练与思维进化
✅ 已规划：
QLoRA 微调

目标：在不牺牲性能的前提下，用你自己的对话数据、情绪偏好、记忆强化等训练模型

建议：结合 PEFT + bitsandbytes 方案，尽量构造高质量、有情绪倾向的对话样本集（包括你和 Estia 的对话）

🔧 可补充：
多 persona 微调（为她训练多个性格侧写如“撒娇模式”“认真助理模式”）

RAG（Retrieval-Augmented Generation）增强知识回忆能力

🧠 二、记忆系统与知识结构
✅ 已规划：
SQLite / TinyDB / JSON 本地存储

embedding 语义记忆（如 text-embedding-3-small）

记忆摘要与去重机制（对话总结、压缩无意义闲聊）

🔧 可补充：
引入“记忆标签”（如 情绪、话题、人名、地点）做结构化记忆分类

添加“回忆指令”或“手动注入记忆”功能（如你可对她说：“记住，我明天早上8点开会”）

🧠 三、人格系统与个性建模
✅ 已规划：
说话风格/思维状态的 prompt 调控

判断对话“重量”来动态调整生成 token 数（max_tokens）

主动发问机制、上下文感知记忆

🔧 可补充：
引入「人格变量」状态：如 mood="gentle"、confidence=0.8 可动态变更其语气

使用“人格记忆池”，定期重构她的世界观/价值观

🗣 四、语音系统与情绪驱动
✅ 已规划：
使用 GPT-SoVITS 替换 edge-tts 实现专属声音

对 Whisper 输出做情绪分类后，驱动语音风格改变

🔧 可补充：
实现“语速”、“语调”控制

加入语音情绪反向驱动，如识别你语音中的悲伤并做出安慰风格回应（情绪双向反馈）

👀 五、视觉系统与屏幕理解
✅ 已规划：
OpenCV 识屏，获取桌面内容

🔧 可补充：
OCR（如 Tesseract 或 mmocr）识别网页文字/字幕

图像内容理解（如集成 CLIP 识图问答）

支持“截图+分析”的指令式对话（如：“Estia，帮我看下这个字幕写了什么？”）

🛠 六、工具与实用辅助功能
✅ 已规划：
写日记/文档、打开程序、联网搜索

翻译网页/视频/游戏内容

支持不调用大模型的轻量辅助功能

🔧 可补充：
集成 ChatOCR + clipboard hook，实现“鼠标悬停翻译”

集成 PDF/Word/网页/视频的“内容摘要器”

🖥 七、界面开发与多终端支持
✅ 已规划：
WPF / C# GUI + 安卓 App

后台挂载与唤醒机制（如监听热词/关键词激活）

🔧 可补充：
加入“桌宠模式”或“语音小精灵模式”

安卓 App 加入小组件 / TTS 聊天栏位（随时互动）

💃 八、虚拟形象与 Live2D 动作控制
✅ 已规划：
Live2D 形象制作与动作驱动

语音情绪联动表情

🔧 可补充：
使用 VTube Studio + WebSocket 插件让 Estia 眨眼、笑、眨动耳朵等

使用 GPT 控制表情状态（如“说这句话时表情设置为微笑+低头”）

📘 终极计划清单（摘要）
分类	功能模块	是否规划	推荐补充
模型训练	QLoRA 微调、多 persona	✅	RAG 记忆检索
记忆系统	本地记忆体、语义检索、摘要重构	✅	标签化结构、注入机制
人格建模	语气、主动性、上下文控制	✅	情绪变量、自定义人设 prompt 池
声音系统	GPT-SoVITS、自适应语气	✅	情绪反驱动
视觉能力	OpenCV 屏幕内容	✅	OCR + 图像理解
工具整合	搜索/翻译/写文档	✅	文档摘要、OCR 工具化
界面支持	GUI/WPF/安卓	✅	桌宠、组件、小窗口交互
虚拟形象	Live2D/VTube驱动	✅	GPT 动作控制、表情情绪联动




```mermaid
flowchart TD
    A1[1️⃣ 初始化数据库\nFAISS 索引 & 缓存加载]
    A2[2️⃣ 用户语音输入\n→ Whisper 转文字]
    A3[3️⃣ 文本向量化\n→ Embedding 模型]
    A4[4️⃣ FAISS 检索相似记忆]
    A5[5️⃣ 联想拓展\n→ memory_association 表]
    A6[6️⃣ 取出原始记忆 + summary\n→ 缓存或 SQLite + session_id]
    A7[7️⃣ 按权重排序 + 去重打分]
    A8[8️⃣ 构建上下文 Prompt\n+ 用户输入 + persona 设定]
    A9[9️⃣ 传入本地 LLM\n→ 生成回复]
    A10[🔟 异步评估总结\n→ 权重估计 + summary 生成]
    A11[1️⃣1️⃣ 写入记忆数据库\n→ memories 表 + summary + embedding]
    A12[1️⃣2️⃣ 自动建立语义关联\n→ memory_association 表]

    C1{{（可选）\n意图/情绪识别}}
    C2{{（可选）\n写入 summary_ref 引用}}
    C3{{（可选）\nMemoryCache 持久化}}

    %% 主流程
    A1 --> A2 --> A3 --> A4 --> A5 --> A6 --> A7 --> A8 --> A9 --> A10 --> A11 --> A12

    %% 支线流程（可选功能）
    A2 --> C1
    A11 --> C2
    A6 --> C3
```


1.初始化向量索引和初始化数据库

memory_association 表

memory_cache

各 memory 层（core/archival/long/short）

自动清理无效/残缺向量（如 key 已丢失）



2.用户语音输入转文本（Whisper）



3.嵌入模型向量化文本（缓存 embedding或加入缓存层避免重复计算）



4.FAISS搜索最相关的记忆（召回前先尝试在 MemoryCache 中命中旧 embedding）



5.关联网络拓展（管理记忆之间的连接关系，memory_association 表），depth = 2 支持多跳推理（多层联想）、类型过滤、打分合并再排序



6.从数据库（数据库memories表，可能是多条）或者记忆缓存中取出对话（防止token不够用，我们可以只有最近的10条对话）并根据关联的session_id找到相关记忆的总结。

示例数据（字段只是参考，可能还会新增别的）：

id content type session_id timestamp

mem001 我今天上班真的好累…… user_input sess_20250627_001 2025-06-27 00:50

mem002 怎么了？想和我聊聊吗？ assistant_reply sess_20250627_001 2025-06-27 00:51

mem003 用户因工作压力感到疲惫，情绪低落。 summary sess_20250627_001 2025-06-27 00:52



7.权重优先排序 + 内容去重（score = weight + 相关性加权 + 是否 summary + 是否最近访问）



8.组装最终上下文 [角色设定] → [核心记忆] → [重要历史/总结] → [新对话] → [你刚才说的话] → [用户输入]



9.传入本地 LLM 生成回复（加入 max_tokens 动态调整）



10.异步请求本地LLM：权重评估 + 本轮总结



11.得到总结与权重评估结果



12.保存对话 + 总结到 memories 表



13.自动关联：将新记忆加入向量库 → 检查语义相似度 → 更新关联表


### 短期优化 (1-2周)
1. **向量检索升级**: 关键词 → 语义向量检索
2. **FAISS集成**: 高效相似度搜索
3. **缓存优化**: 多层记忆缓存
4. **性能调优**: 批处理和并发优化

### 中期扩展 (1-2月)
1. **关联网络**: 记忆间智能关联
2. **个性化学习**: 用户行为适应
3. **多模态支持**: 图像、音频记忆
4. **主题聚类**: 自动话题发现

### 长期愿景 (3-6月)
1. **分布式存储**: 大规模记忆管理
2. **实时学习**: 在线学习和适应
3. **知识图谱**: 结构化知识表示
4. **情感记忆**: 情感状态感知