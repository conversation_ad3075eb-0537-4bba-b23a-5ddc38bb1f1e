---
description: 开发工作流程规则
globs: 
alwaysApply: true
---

# 开发工作流程规则

## 核心原则

### 1. 单一问题专注
- **每次只解决一个具体问题**
- 不要在一次对话中引入多个复杂功能
- 确保用户能完全理解每个变化

### 2. 解释先行
- **先解释原理和思路**
- 说明为什么要这样做
- 让用户确认理解后再写代码

### 3. 渐进式改进
- 避免大爆炸式重写
- 每次改动都要保持系统可运行
- 优先解决影响最大的问题

### 4. 用户可控
- 确保用户能理解代码逻辑
- 避免过度复杂的抽象
- 保持代码的可读性和可维护性

## 实施流程

1. **问题识别**：明确当前要解决的具体问题
2. **方案解释**：详细说明解决思路和原理
3. **用户确认**：等待用户确认理解和同意
4. **代码实现**：编写简洁清晰的代码
5. **测试验证**：确保功能正常工作
6. **总结反馈**：说明完成了什么，下一步是什么

## 注意事项

- 不要提出过于复杂的技术方案
- 重视用户的反馈和担忧
- 保持项目的整体稳定性
- 优先实用性而非技术炫技 