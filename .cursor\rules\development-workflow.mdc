---
description: 
globs: 
alwaysApply: true
---
# 开发工作流指南

## 🎯 开发核心原则

### 1. 单一问题专注
- **每次只解决一个具体问题**
- 不要在一次对话中引入多个复杂功能
- 确保用户能完全理解每个变化

### 2. 解释先行
- **先解释原理和思路**
- 说明为什么要这样做
- 让用户确认理解后再写代码

### 3. 渐进式改进
- 避免大爆炸式重写
- 每次改动都要保持系统可运行
- 优先解决影响最大的问题

### 4. 用户可控
- 确保用户能理解代码逻辑
- 避免过度复杂的抽象
- 保持代码的可读性和可维护性，功能模块化，结构简洁易懂
- 除非是用户要求，否则不要随意删除核心代码文件和目录，如果必须删除请说明为什么

## 📋 实施流程

1. **问题识别**：明确当前要解决的具体问题
2. **方案解释**：详细说明解决思路和原理
3. **用户确认**：等待用户确认理解和同意
4. **代码实现**：编写简洁清晰的代码
5. **测试验证**：确保功能正常工作
6. **总结反馈**：说明完成了什么，下一步是什么

## 项目设置

### 环境准备
1. 使用 [activate.bat](mdc:activate.bat) 激活虚拟环境
2. 参考 [setup/INSTALL_STEPS.md](mdc:setup/INSTALL_STEPS.md) 进行完整安装
3. 运行 [setup/check_env.py](mdc:setup/check_env.py) 检查环境配置

### 项目启动
- 使用 [start.bat](mdc:start.bat) 启动完整系统
- 或直接运行 [main.py](mdc:main.py) 进行开发调试

## 开发脚本

### 数据库管理
- [scripts/build_index.py](mdc:scripts/build_index.py) - 构建向量索引
- [scripts/fix_database_schema.py](mdc:scripts/fix_database_schema.py) - 修复数据库架构
- [scripts/migrate_id_column.py](mdc:scripts/migrate_id_column.py) - 数据库迁移

## 配置管理

### 主配置文件
- [config/settings.py](mdc:config/settings.py) - 全局配置设置

### 工具配置
- [core/utils/config_loader.py](mdc:core/utils/config_loader.py) - 配置加载器
- [core/utils/logger.py](mdc:core/utils/logger.py) - 日志系统

## 开发规范
1. **代码组织** - 按功能模块分目录
2. **配置管理** - 统一配置文件管理
3. **日志记录** - 详细的调试和错误日志
4. **错误处理** - 优雅的异常处理机制

## 调试技巧
- 查看 `logs/` 目录中的日志文件
- 使用 `temp/` 目录存储临时调试文件
- 利用配置文件切换调试模式
- 使用分层测试方法定位问题

## 部署准备
- 检查 [README.md](mdc:README.md) 和 [README_INSTALL.md](mdc:README_INSTALL.md)
- 确保所有依赖已正确安装
- 验证配置文件和环境变量



