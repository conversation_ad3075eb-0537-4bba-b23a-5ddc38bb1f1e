---
description: 
globs: 
alwaysApply: false
---
# 代码规范指南

## 项目结构原则
- 按功能模块组织代码，避免循环依赖
- 功能模块化，结构简洁易懂
- 配置文件统一管理在 `config/` 目录
- 工具函数放在 `core/utils/` 目录
- 测试文件放在 `tests/` 目录

## 中文注释规范
- 所有代码注释使用简体中文
- 类和函数的docstring使用中文描述
- 变量名使用英文，但注释解释使用中文
- 日志信息使用中文，便于调试

## 错误处理规范
- 使用try-except捕获异常，提供中文错误信息
- 记录详细的错误日志，包含上下文信息
- 优雅降级，避免系统崩溃
- 对外部API调用进行超时和重试处理

## 配置管理规范
- 所有配置项在 [config/settings.py](mdc:config/settings.py) 中定义
- 使用环境变量覆盖默认配置
- 敏感信息（API密钥）不提交到版本控制
- 提供配置验证和默认值

## 日志记录规范
- 使用 [core/utils/logger.py](mdc:core/utils/logger.py) 统一日志管理
- 日志级别：DEBUG（调试）、INFO（信息）、WARNING（警告）、ERROR（错误）
- 关键操作记录INFO级别日志
- 异常情况记录ERROR级别日志

## 测试规范
- 每个模块都应有对应的测试文件
- 测试文件命名格式：`test_模块名.py`
- 使用pytest框架进行测试
- 测试覆盖正常流程和异常情况

## 性能优化规范
- 使用缓存机制减少重复计算
- 批量处理数据库操作
- 异步处理耗时操作
- 定期监控和优化性能瓶颈

