# Web监控仪表板问题修复指南

## 🔧 已修复的问题

### 1. JavaScript函数未定义错误
**问题**: `refreshAllData is not defined`
**原因**: 函数定义在局部作用域内，HTML按钮无法访问
**解决方案**: 
- 创建全局函数 `window.manualRefresh()`
- 修改按钮调用为 `onclick="window.manualRefresh()"`

### 2. 数据加载问题
**问题**: 页面显示"加载中"，没有数据
**原因**: 监控器可能没有真实数据
**解决方案**:
- 添加了模拟监控器作为降级方案
- 创建了测试数据生成API
- 添加了"加载测试数据"按钮

## 🚀 启动方法

### 方法1: 完整版仪表板
```bash
python start_dashboard.py
```
访问: http://localhost:5000

### 方法2: 测试版仪表板（推荐用于调试）
```bash
python test_dashboard.py
```
访问: http://localhost:5001

## 🧪 测试功能

### 加载测试数据
1. 打开仪表板页面
2. 点击右下角绿色的"🧪 加载测试数据"按钮
3. 查看各个组件是否正常显示

### 手动刷新
1. 点击蓝色的"🔄 刷新数据"按钮
2. 观察数据是否更新

## 🔍 调试步骤

### 1. 检查控制台错误
打开浏览器开发者工具(F12)，查看Console标签页是否有错误信息

### 2. 检查网络请求
在Network标签页查看API请求是否成功：
- `/api/dashboard_data` - 批量数据API
- `/api/status` - 状态API

### 3. 检查服务器日志
观察Python控制台输出，查看是否有错误信息

## 🛠️ 常见问题解决

### 问题1: 端口被占用
```
OSError: [WinError 10048] 通常每个套接字地址只允许使用一次
```
**解决方案**: 
- 更改端口号
- 或者杀死占用端口的进程

### 问题2: 模板文件找不到
```
TemplateNotFound: dashboard.html
```
**解决方案**:
- 确保在项目根目录运行
- 检查templates文件夹是否存在

### 问题3: 依赖包缺失
```
ModuleNotFoundError: No module named 'flask'
```
**解决方案**:
```bash
pip install flask flask-socketio
```

### 问题4: 词云不显示
**可能原因**: WordCloud库未加载或网络问题
**解决方案**: 检查CDN链接是否可访问

## 📊 性能优化验证

### 检查优化效果
1. 打开浏览器开发者工具
2. 查看Console输出的性能报告（每分钟一次）
3. 观察以下指标：
   - 平均API耗时应 < 500ms
   - 平均渲染耗时应 < 100ms
   - 错误次数应为0

### 网络优化验证
1. 在Network标签页观察请求数量
2. 应该看到批量请求而不是多个并发请求
3. 缓存命中时请求数量会减少

## 🔄 功能测试清单

- [ ] 页面正常加载
- [ ] 实时状态显示正常
- [ ] 性能统计显示正常
- [ ] 关键词云生成正常
- [ ] 性能趋势图显示正常
- [ ] 记忆分析图显示正常
- [ ] 会话列表显示正常
- [ ] 手动刷新功能正常
- [ ] 测试数据加载正常
- [ ] 通知系统工作正常
- [ ] WebSocket连接正常（如果使用完整版）

## 📞 获取帮助

如果仍有问题，请检查：
1. Python版本 >= 3.7
2. 所有依赖包已安装
3. 防火墙设置
4. 浏览器兼容性（推荐Chrome/Firefox）

## 🎯 下一步

1. 确认基本功能正常后，可以集成真实的监控数据
2. 根据实际需求调整刷新频率和缓存策略
3. 添加更多自定义监控指标
