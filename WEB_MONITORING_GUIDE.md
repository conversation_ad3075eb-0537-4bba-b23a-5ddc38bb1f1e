# Estia AI Web监控仪表板使用指南

## 🎯 快速开始

### 步骤1: 启动Estia主程序
```bash
python main.py --mode text
```

等待看到以下提示：
```
🎉 Estia AI助手启动完成！(完整13步记忆系统)
⚡ 总启动时间: 6.93秒
💬 Estia 文本交互模式
```

### 步骤2: 测试实时数据连接
```bash
python test_live_connection.py
```

应该看到：
```
🔍 测试实时数据连接...
系统运行状态: ✅ 运行中
📊 系统健康状态:
  database_accessible: True
  system_status: active
🧠 记忆统计:
  总记忆数: 6
  今日新增: 2
```

### 步骤3: 启动Web监控仪表板
```bash
python start_dashboard.py
```

### 步骤4: 访问监控界面
打开浏览器访问: **http://localhost:5000/fixed**

### 步骤5: 与Estia对话生成数据
在主程序中输入一些问题：
```
👤 你: 你好，请介绍一下你自己
👤 你: 你能帮我做什么？
👤 你: 记住我喜欢喝咖啡
```

然后在Web界面点击"🔄 刷新数据"查看实时更新！

## 📊 界面功能说明

### 数据源指示器
页面顶部会显示当前数据源：
- 🔴 **实时数据**: 连接到运行中的Estia系统
- 🧪 **测试数据**: 模拟数据用于演示
- ⚠️ **模拟监控**: Estia系统未运行

### 监控面板

#### 📊 实时状态
- **系统状态**: 运行中/空闲
- **当前会话**: 最新的会话ID
- **运行时间**: 系统运行时长
- **进度**: 当前处理进度

#### 📈 性能统计
- **总会话数**: 历史会话总数
- **平均耗时**: 平均响应时间
- **成功率**: 处理成功率
- **最慢步骤**: 性能瓶颈分析

#### ☁️ 关键词云
- 从对话中提取的热门关键词
- 词汇大小表示使用频率
- 动态更新反映最新对话内容

#### 📊 性能趋势
- 会话响应时间趋势图
- 帮助识别性能变化

#### 🧠 记忆分析
- **平均相似度**: 记忆检索的相似度
- **检索次数**: 记忆系统调用次数
- **关联拓展**: 关联网络扩展情况
- **相似度分布图**: 高/中/低相似度记忆分布

#### 💬 最近会话
- 显示最近的对话记录
- 包含用户输入和AI回复
- 会话时长和成功/失败统计

## 🔧 功能按钮

### 🔄 刷新数据
- 手动刷新所有监控数据
- 从正在运行的Estia系统获取最新信息

### 🧪 加载测试数据
- 加载模拟数据用于界面演示
- 当Estia系统未运行时使用

## 🚨 故障排除

### 问题1: 显示"模拟监控"
**原因**: Estia主程序未运行
**解决**: 
1. 启动主程序: `python main.py --mode text`
2. 刷新Web页面

### 问题2: 数据不更新
**原因**: 缓存或连接问题
**解决**:
1. 点击"🔄 刷新数据"按钮
2. 检查主程序是否正常运行
3. 重启Web仪表板

### 问题3: JavaScript错误
**原因**: 浏览器兼容性或代码问题
**解决**:
1. 使用Chrome或Firefox浏览器
2. 访问修复版: http://localhost:5000/fixed
3. 清除浏览器缓存

### 问题4: 端口被占用
**原因**: 5000端口已被其他程序使用
**解决**:
1. 关闭占用端口的程序
2. 或修改启动脚本使用其他端口

## 📈 最佳实践

### 1. 实时监控工作流
1. 启动Estia主程序
2. 启动Web监控仪表板
3. 进行对话测试
4. 观察监控数据变化
5. 分析性能和记忆模式

### 2. 性能分析
- 关注平均响应时间趋势
- 监控记忆检索效率
- 观察关键词变化模式
- 分析会话成功率

### 3. 记忆系统优化
- 通过关键词云了解对话主题
- 监控记忆相似度分布
- 观察关联网络扩展情况
- 分析记忆权重分布

## 🔮 高级功能

### WebSocket实时更新
- 自动接收系统状态更新
- 无需手动刷新即可看到变化
- 实时性能监控

### 智能缓存
- 减少重复数据请求
- 提高界面响应速度
- 智能刷新策略

### 性能优化
- 批量数据获取
- 前端渲染优化
- 内存使用优化

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查Python控制台输出
3. 参考TROUBLESHOOTING.md文档
4. 使用快速测试版验证基本功能

## 🎉 享受监控体验！

现在你可以实时观察Estia AI的内部工作过程，了解记忆系统如何处理和存储信息，以及性能表现如何。这对于系统优化和问题诊断非常有帮助！
