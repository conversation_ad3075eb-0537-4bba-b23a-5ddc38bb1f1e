#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Web监控仪表板
"""

import sys
sys.path.append('.')

from web.web_dashboard import app, socketio

if __name__ == '__main__':
    print("🚀 启动Estia记忆监控仪表板...")
    print("📊 访问地址: http://localhost:5000")
    print("🔄 实时监控已启用")
    print("⚠️  按 Ctrl+C 停止服务器")
    
    try:
        # 启动Flask-SocketIO服务器
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 仪表板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")