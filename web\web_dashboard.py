#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Estia 记忆监控 Web 仪表板
========================

基于Flask的实时监控可视化界面，包含：
- 实时流程监控
- 性能图表和分析
- 关键词云和趋势分析
- 记忆内容可视化
"""

import json
import time
import re
from datetime import datetime, timedelta
from collections import Counter, defaultdict
from typing import Dict, List, Any, Optional

from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_socketio import SocketIO, emit
import threading

# 导入监控系统
from core.memory.managers.monitor_flow.monitoring import (
    MemoryPipelineMonitor,
    MemoryPipelineStep,
    StepStatus,
    MonitorAnalytics
)

app = Flask(__name__, template_folder='../templates')
app.config['SECRET_KEY'] = 'estia_monitoring_secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局监控实例
monitor = MemoryPipelineMonitor.get_instance()
analytics = MonitorAnalytics(monitor)

# 尝试连接v6.0的MemoryFlowMonitor
try:
    from core.memory.managers.monitor_flow import MemoryFlowMonitor
    
    # 创建模拟的组件字典来初始化MemoryFlowMonitor
    # 在实际使用中，这些组件应该来自真实的v6.0系统
    mock_components = {
        'db_manager': None,  # 数据库管理器
        'unified_cache': None,  # 统一缓存
        'sync_flow_manager': None,  # 同步流程管理器
        'async_flow_manager': None  # 异步流程管理器
    }
    
    flow_monitor = MemoryFlowMonitor(mock_components)
    print("✅ v6.0 MemoryFlowMonitor 初始化成功")
except ImportError as e:
    flow_monitor = None
    print(f"⚠️ v6.0 MemoryFlowMonitor 导入失败: {e}")
except Exception as e:
    flow_monitor = None
    print(f"⚠️ v6.0 MemoryFlowMonitor 初始化失败: {e}")


class KeywordAnalyzer:
    """关键词分析器"""
    
    def __init__(self):
        # 中文停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '什么', '我们', '他们', '她们', '它们', '这个', '那个',
            '怎么', '为什么', '如何', '吗', '呢', '吧', '啊', '呀'
        }
        
        # 英文停用词
        self.stop_words.update({
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'could',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them'
        })
    
    def extract_keywords(self, text: str, min_length: int = 2) -> List[str]:
        """提取关键词"""
        if not text:
            return []
        
        # 清理文本
        text = text.lower()
        # 保留中文、英文和数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        
        # 分词（简单的基于空格和标点的分词）
        words = text.split()
        
        # 过滤关键词
        keywords = []
        for word in words:
            word = word.strip()
            if (len(word) >= min_length and 
                word not in self.stop_words and
                not word.isdigit()):
                keywords.append(word)
        
        return keywords
    
    def analyze_keyword_trends(self, sessions: List) -> Dict[str, Any]:
        """分析关键词趋势"""
        keyword_counts = Counter()
        time_series = defaultdict(list)
        
        for session in sessions:
            if hasattr(session, 'user_input') and session.user_input:
                keywords = self.extract_keywords(session.user_input)
                
                for keyword in keywords:
                    keyword_counts[keyword] += 1
                    time_series[keyword].append(session.start_time)
            
            # 分析AI回复中的关键词
            if hasattr(session, 'ai_response') and session.ai_response:
                response_keywords = self.extract_keywords(session.ai_response)
                for keyword in response_keywords[:5]:  # 只取前5个避免过多
                    keyword_counts[f"回复_{keyword}"] += 1
        
        # 计算趋势
        trending_keywords = []
        for keyword, count in keyword_counts.most_common(20):
            if count >= 2:  # 至少出现2次
                trending_keywords.append({
                    'word': keyword,
                    'count': count,
                    'frequency': count / len(sessions) if sessions else 0
                })
        
        return {
            'top_keywords': trending_keywords,
            'total_unique_keywords': len(keyword_counts),
            'keyword_distribution': dict(keyword_counts.most_common(10))
        }


class V6DataAdapter:
    """v6.0数据适配器，将v6.0监控数据转换为仪表板期望的格式"""
    
    def __init__(self, flow_monitor):
        self.flow_monitor = flow_monitor
    
    def adapt_comprehensive_stats(self) -> Dict[str, Any]:
        """适配综合统计数据"""
        if not self.flow_monitor:
            return {'error': 'v6.0监控器不可用'}
        
        try:
            stats = self.flow_monitor.get_comprehensive_stats()
            
            # 转换为仪表板期望的格式
            adapted_stats = {
                'timestamp': stats.get('timestamp', 0),
                'monitor_status': stats.get('monitor_status', 'unknown'),
                'performance_summary': {
                    'cache_hit_rate': stats.get('performance_metrics', {}).get('cache_hit_rate', 0),
                    'cache_efficiency': stats.get('performance_metrics', {}).get('cache_efficiency', 0),
                    'system_health': stats.get('health_status', {}).get('status', 'unknown')
                },
                'memory_overview': stats.get('memory_overview', {}),
                'session_stats': stats.get('session_statistics', {})
            }
            
            return adapted_stats
            
        except Exception as e:
            return {'error': f'适配综合统计失败: {str(e)}'}
    
    def adapt_13_step_monitoring(self) -> Dict[str, Any]:
        """适配13步流程监控数据"""
        if not self.flow_monitor:
            return {'error': 'v6.0监控器不可用'}
        
        try:
            step_data = self.flow_monitor.get_13_step_monitoring()
            
            # 转换为仪表板期望的格式
            if 'error' in step_data:
                return step_data
            
            adapted_data = {
                'total_steps': step_data.get('total_steps', 15),
                'sync_steps': step_data.get('sync_steps', 9),
                'async_steps': step_data.get('async_steps', 6),
                'step_performance': step_data.get('overall_performance', {}),
                'step_details': step_data.get('step_details', {}),
                'timestamp': step_data.get('timestamp', 0)
            }
            
            return adapted_data
            
        except Exception as e:
            return {'error': f'适配13步监控失败: {str(e)}'}
    
    def adapt_real_time_metrics(self) -> Dict[str, Any]:
        """适配实时性能指标"""
        if not self.flow_monitor:
            return {'error': 'v6.0监控器不可用'}
        
        try:
            metrics = self.flow_monitor.get_real_time_metrics()
            
            # 转换为仪表板期望的格式
            if 'error' in metrics:
                return metrics
            
            adapted_metrics = {
                'cache_performance': metrics.get('cache_performance', {}),
                'database_performance': metrics.get('database_performance', {}),
                'queue_status': metrics.get('queue_status', {}),
                'memory_usage': metrics.get('memory_usage', {}),
                'timestamp': metrics.get('timestamp', 0)
            }
            
            return adapted_metrics
            
        except Exception as e:
            return {'error': f'适配实时指标失败: {str(e)}'}


class MemoryContentAnalyzer:
    """记忆内容分析器"""
    
    def analyze_memory_patterns(self, sessions: List) -> Dict[str, Any]:
        """分析记忆模式"""
        memory_types = Counter()
        similarity_scores = []
        memory_usage = defaultdict(int)
        
        for session in sessions:
            for step, metrics in session.steps.items():
                if step == MemoryPipelineStep.STEP_5_FAISS_SEARCH:
                    # 分析检索结果
                    if 'avg_similarity' in metrics.metadata:
                        similarity_scores.append(metrics.metadata['avg_similarity'])
                    
                    if 'result_count' in metrics.metadata:
                        memory_usage['retrieved'] += metrics.metadata['result_count']
                
                elif step == MemoryPipelineStep.STEP_6_ASSOCIATION_EXPAND:
                    # 分析关联拓展
                    if 'expansion_count' in metrics.metadata:
                        memory_usage['associations'] += metrics.metadata['expansion_count']
                
                elif step == MemoryPipelineStep.STEP_9_CONTEXT_BUILD:
                    # 分析上下文构建
                    if 'memory_used' in metrics.metadata:
                        memory_usage['context_memories'] += metrics.metadata['memory_used']
        
        avg_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0
        
        return {
            'average_similarity': avg_similarity,
            'memory_usage_stats': dict(memory_usage),
            'total_retrievals': len(similarity_scores),
            'similarity_distribution': self._calculate_similarity_distribution(similarity_scores)
        }
    
    def _calculate_similarity_distribution(self, scores: List[float]) -> Dict[str, int]:
        """计算相似度分布"""
        if not scores:
            return {}
        
        bins = {'高 (>0.8)': 0, '中 (0.6-0.8)': 0, '低 (<0.6)': 0}
        
        for score in scores:
            if score > 0.8:
                bins['高 (>0.8)'] += 1
            elif score > 0.6:
                bins['中 (0.6-0.8)'] += 1
            else:
                bins['低 (<0.6)'] += 1
        
        return bins


# 初始化分析器
keyword_analyzer = KeywordAnalyzer()
memory_analyzer = MemoryContentAnalyzer()

# 初始化v6.0数据适配器
v6_adapter = V6DataAdapter(flow_monitor) if flow_monitor else None


@app.route('/')
def dashboard():
    """主仪表板页面"""
    return render_template('dashboard.html')


@app.route('/api/status')
def get_status():
    """获取实时状态"""
    status = analytics.get_real_time_status()
    summary = monitor.get_performance_summary()
    
    return jsonify({
        'status': status,
        'summary': summary,
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/performance')
def get_performance():
    """获取性能数据"""
    if len(monitor.completed_sessions) == 0:
        return jsonify({'error': '暂无数据'})
    
    report = analytics.generate_performance_report()
    bottlenecks = analytics.analyze_bottlenecks()
    
    # 转换为字典格式
    import dataclasses
    return jsonify({
        'report': dataclasses.asdict(report),
        'bottlenecks': dataclasses.asdict(bottlenecks),
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/keywords')
def get_keywords():
    """获取关键词分析"""
    sessions = monitor.completed_sessions
    
    if not sessions:
        return jsonify({'error': '暂无数据'})
    
    keyword_data = keyword_analyzer.analyze_keyword_trends(sessions)
    
    return jsonify({
        'keywords': keyword_data,
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/memory_analysis')
def get_memory_analysis():
    """获取记忆分析"""
    sessions = monitor.completed_sessions
    
    if not sessions:
        return jsonify({'error': '暂无数据'})
    
    memory_data = memory_analyzer.analyze_memory_patterns(sessions)
    
    return jsonify({
        'memory': memory_data,
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/sessions')
def get_sessions():
    """获取会话列表"""
    sessions = monitor.completed_sessions[-20:]  # 最近20个会话
    
    session_data = []
    for session in sessions:
        session_info = {
            'session_id': session.session_id,
            'start_time': datetime.fromtimestamp(session.start_time).isoformat(),
            'duration': session.total_duration or 0,
            'success_count': session.success_count,
            'failed_count': session.failed_count,
            'user_input': session.user_input or '',
            'ai_response': (session.ai_response or '')[:100] + '...' if session.ai_response and len(session.ai_response) > 100 else session.ai_response or ''
        }
        session_data.append(session_info)
    
    return jsonify({
        'sessions': session_data,
        'total': len(monitor.completed_sessions),
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/step_details/<step_name>')
def get_step_details(step_name):
    """获取特定步骤的详细信息"""
    step_data = []
    
    for session in monitor.completed_sessions:
        for step, metrics in session.steps.items():
            if step.value == step_name:
                step_info = {
                    'session_id': session.session_id,
                    'duration': metrics.duration or 0,
                    'status': metrics.status.value,
                    'input_size': metrics.input_size or 0,
                    'output_size': metrics.output_size or 0,
                    'metadata': metrics.metadata,
                    'timestamp': datetime.fromtimestamp(metrics.start_time).isoformat()
                }
                step_data.append(step_info)
    
    return jsonify({
        'step_name': step_name,
        'executions': step_data,
        'count': len(step_data),
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/v6_comprehensive_stats')
def get_v6_comprehensive_stats():
    """获取v6.0系统的综合统计信息"""
    if v6_adapter is None:
        return jsonify({'error': 'v6.0数据适配器不可用'})
    
    try:
        adapted_stats = v6_adapter.adapt_comprehensive_stats()
        return jsonify({
            'v6_stats': adapted_stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': f'获取v6.0统计失败: {str(e)}'})


@app.route('/api/13_step_monitoring')
def get_13_step_monitoring():
    """获取13步流程监控详情"""
    if v6_adapter is None:
        return jsonify({'error': 'v6.0数据适配器不可用'})
    
    try:
        adapted_data = v6_adapter.adapt_13_step_monitoring()
        return jsonify({
            '13_step_data': adapted_data,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': f'获取13步监控失败: {str(e)}'})


@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    print('客户端已连接')
    emit('message', {'data': '监控连接已建立'})


@socketio.on('start_monitoring')
def handle_start_monitoring():
    """开始实时监控"""
    print('开始实时监控')
    
    def monitoring_loop():
        while True:
            try:
                # 获取实时状态
                status = analytics.get_real_time_status()
                summary = monitor.get_performance_summary()
                
                # 发送实时数据
                socketio.emit('status_update', {
                    'status': status,
                    'summary': summary,
                    'timestamp': datetime.now().isoformat()
                })
                
                time.sleep(2)  # 每2秒更新一次
                
            except Exception as e:
                print(f"监控循环错误: {e}")
                break
    
    # 在后台线程中运行监控
    monitoring_thread = threading.Thread(target=monitoring_loop)
    monitoring_thread.daemon = True
    monitoring_thread.start()


# 注意：现在使用独立的模板文件 templates/dashboard.html


def run_dashboard(host='127.0.0.1', port=5000, debug=True):
    """运行Web仪表板"""
    print(f"🚀 启动 Estia 记忆监控仪表板")
    print(f"📊 访问地址: http://{host}:{port}")
    print(f"🔄 实时监控: WebSocket 连接已启用")
    print("="*60)
    
    # 启动Flask应用
    socketio.run(app, host=host, port=port, debug=debug)


if __name__ == '__main__':
    run_dashboard()