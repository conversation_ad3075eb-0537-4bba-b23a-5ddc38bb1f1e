#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时数据连接
"""

import sys
sys.path.append('.')

from web.live_data_connector import live_connector
import json

def test_live_connection():
    """测试实时数据连接"""
    print("🔍 测试实时数据连接...")
    print("="*50)
    
    # 检查系统是否运行
    is_running = live_connector.check_system_running()
    print(f"系统运行状态: {'✅ 运行中' if is_running else '❌ 未运行'}")
    
    if not is_running:
        print("请先启动Estia主程序 (python main.py --mode text)")
        return False
    
    # 获取系统健康状态
    print("\n📊 系统健康状态:")
    health = live_connector.get_system_health()
    for key, value in health.items():
        if key != 'timestamp':
            print(f"  {key}: {value}")
    
    # 获取记忆统计
    print("\n🧠 记忆统计:")
    memory_stats = live_connector.get_memory_statistics()
    if 'error' not in memory_stats:
        print(f"  总记忆数: {memory_stats.get('total_memories', 0)}")
        print(f"  今日新增: {memory_stats.get('today_memories', 0)}")
        print(f"  权重分布: {memory_stats.get('weight_distribution', {})}")
    else:
        print(f"  错误: {memory_stats['error']}")
    
    # 获取会话统计
    print("\n💬 会话统计:")
    session_stats = live_connector.get_session_statistics()
    if 'error' not in session_stats:
        print(f"  总会话数: {session_stats.get('total_sessions', 0)}")
        print(f"  最近对话数: {len(session_stats.get('recent_dialogues', []))}")
    else:
        print(f"  错误: {session_stats['error']}")
    
    # 获取综合数据
    print("\n📈 综合数据测试:")
    comprehensive = live_connector.get_comprehensive_data()
    print(f"  数据获取: {'✅ 成功' if comprehensive['system_running'] else '❌ 失败'}")
    
    return True

if __name__ == '__main__':
    success = test_live_connection()
    
    if success:
        print("\n🎉 实时数据连接测试完成！")
        print("现在可以启动Web仪表板查看实时数据:")
        print("python start_dashboard.py")
        print("访问: http://localhost:5000/fixed")
    else:
        print("\n❌ 实时数据连接测试失败")
        print("请确保Estia主程序正在运行")
