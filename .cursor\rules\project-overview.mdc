---
description: 
globs: 
alwaysApply: false
---
# Estia AI助手项目概览

## 项目简介
Estia是一个智能AI助手项目，具有多层记忆系统、语音交互和游戏视觉识别功能。

## 核心架构
- **入口点**: [main.py](mdc:main.py) - 主程序入口
- **核心应用**: [core/app.py](mdc:core/app.py) - 应用主逻辑
- **配置管理**: [config/settings.py](mdc:config/settings.py) - 全局配置
- **启动脚本**: [start.bat](mdc:start.bat) - Windows启动脚本

## 主要功能模块
1. **记忆系统** - 多层次记忆管理和检索
2. **对话系统** - 智能对话引擎和个性化
3. **音频系统** - 语音输入输出和键盘控制
4. **视觉系统** - 游戏画面识别和分析
5. **工具系统** - 配置加载、日志记录等

## 开发环境
- Python环境，使用虚拟环境管理
- 支持本地LLM和多种API提供商
- Windows PowerShell环境
- 使用FAISS向量检索和SQLite数据库

## 项目简介
Estia是一个智能AI助手项目，具有多层记忆系统、语音交互和游戏视觉识别功能。

## 核心架构
- **入口点**: [main.py](mdc:main.py) - 主程序入口
- **核心应用**: [core/app.py](mdc:core/app.py) - 应用主逻辑
- **配置管理**: [config/settings.py](mdc:config/settings.py) - 全局配置
- **启动脚本**: [start.bat](mdc:start.bat) - Windows启动脚本

## 主要功能模块
1. **记忆系统** - 多层次记忆管理和检索
2. **对话系统** - 智能对话引擎和个性化
3. **音频系统** - 语音输入输出和键盘控制
4. **视觉系统** - 游戏画面识别和分析
5. **工具系统** - 配置加载、日志记录等

## 开发环境
- Python环境，使用虚拟环境管理
- 支持本地LLM和多种API提供商
- Windows PowerShell环境
- 使用FAISS向量检索和SQLite数据库


