# 统一缓存系统设计方案

## 1. 背景

Estia系统目前存在三个部分重叠的缓存系统：

1. **EnhancedMemoryCache**（位于`core/memory/embedding/cache.py`）- 嵌入向量缓存
2. **CacheManager**（位于`core/memory/memory_cache/cache_manager.py`）- 智能缓存管理
3. **SmartRetriever的内部缓存**（位于`core/memory/retrieval/smart_retriever.py`）- 检索器缓存

这些系统各自独立发展，导致以下问题：

- 数据重复缓存，造成内存浪费
- 缓存失效不同步，导致数据不一致
- 缓存策略不统一，难以优化
- 缓存键冲突，可能导致数据错误
- 代码重复，维护成本高

## 2. 重构目标

创建统一的缓存管理系统，具有以下特点：

- **统一接口** - 所有缓存实现共用一套标准API
- **事件驱动** - 通过事件系统实现缓存间协调
- **分层架构** - 支持多级缓存策略
- **可扩展** - 易于添加新的缓存实现
- **高性能** - 优化缓存访问和同步策略
- **可监控** - 提供详细的统计和监控功能

## 3. 系统设计

### 3.1 整体架构

新的缓存系统采用分层设计：

```
┌───────────────────────────────────────┐
│       UnifiedCacheManager             │ ← 统一缓存管理器
├───────────────────────────────────────┤
│                                       │
│  ┌─────────┐  ┌─────────┐  ┌────────┐ │
│  │ Hot     │  │ Warm    │  │Persist │ │ ← 不同级别缓存
│  │ Cache   │  │ Cache   │  │Cache   │ │
│  └─────────┘  └─────────┘  └────────┘ │
│                                       │
├───────────────────────────────────────┤
│           CacheInterface              │ ← 统一接口定义
└───────────────────────────────────────┘
```

### 3.2 核心组件

#### 3.2.1 缓存接口 (`CacheInterface`)

定义统一的缓存操作接口，包括：

- 基本操作：`get`、`put`、`delete`、`contains`等
- 元数据管理：`get_metadata`、`update_metadata`等
- 事件通知：`notify_listeners`、`add_listener`等
- 统计信息：`get_stats`、`get_size`等

#### 3.2.2 缓存事件系统

事件驱动的通信机制，支持：

- 事件类型：如`GET`、`PUT`、`DELETE`、`CLEAR`、`PROMOTE`等
- 事件监听：各缓存实现可监听其他缓存的事件
- 元数据共享：通过事件传递缓存项的元数据

#### 3.2.3 统一缓存管理器

中央控制器，负责：

- 缓存注册与管理
- 统一访问接口
- 缓存策略执行
- 缓存协调与同步
- 统计信息收集

#### 3.2.4 基础缓存实现

通用基础类，提供：

- 通用缓存功能实现
- 线程安全机制
- 过期管理
- 淘汰策略

### 3.3 缓存级别

系统定义多个缓存级别，针对不同场景：

- **HOT** - 热点数据，高频访问
- **WARM** - 温缓存，中频访问
- **COLD** - 冷缓存，低频访问
- **PERSISTENT** - 持久化缓存
- **EXTERNAL** - 外部缓存系统

## 4. 关键流程

### 4.1 缓存查找流程

1. 首先检查键映射表，快速定位缓存项
2. 按缓存级别优先级顺序查找（HOT→WARM→COLD→PERSISTENT）
3. 命中时更新访问统计并考虑是否提升
4. 未命中时记录并返回默认值

### 4.2 缓存更新流程

1. 添加项到指定级别的缓存
2. 更新键映射表
3. 根据配置同步到其他级别缓存
4. 通知事件给所有监听器

### 4.3 缓存协调机制

1. 使用事件系统在缓存间传递更改
2. 键映射表维护项目位置信息
3. 定期维护同步缓存状态
4. 冲突解决策略确保数据一致性

## 5. 具体实现计划

### 5.1 核心文件

- `core/memory/caching/cache_interface.py` - 定义缓存接口和事件系统
- `core/memory/caching/cache_manager.py` - 实现统一缓存管理器
- `core/memory/caching/base_cache.py` - 基础缓存实现
- `core/memory/caching/__init__.py` - 包导出

### 5.2 具体缓存实现

- `core/memory/caching/memory_cache.py` - 内存缓存实现
- `core/memory/caching/persistent_cache.py` - 持久化缓存实现
- `core/memory/caching/vector_cache.py` - 向量特化缓存实现

### 5.3 适配层

- `core/memory/embedding/cache_adapter.py` - 向量缓存适配器
- `core/memory/memory_cache/cache_adapter.py` - 记忆缓存适配器
- `core/memory/retrieval/cache_adapter.py` - 检索缓存适配器

## 6. 迁移策略

采用渐进式迁移，步骤如下：

1. 构建新缓存核心框架
2. 为每个现有缓存创建适配器
3. 逐个替换现有缓存实现
4. 调整业务代码使用新的统一接口
5. 全面测试并监控性能
6. 移除旧缓存代码

## 7. 性能考量

- 使用键映射表加速缓存定位
- 实现线程安全但避免全局锁
- 优化序列化和反序列化
- 批量操作支持
- 缓存预热机制

## 8. 监控与维护

- 详细的缓存统计信息
- 性能指标收集
- 自动维护机制
- 调试和日志支持

## 9. 下一步计划

1. **第一阶段**：实现核心接口和基础类
2. **第二阶段**：实现统一缓存管理器
3. **第三阶段**：创建具体缓存实现
4. **第四阶段**：开发适配层和迁移代码
5. **第五阶段**：全面测试和性能优化 