---
description: 
globs: 
alwaysApply: false
---
# 测试指南

## 测试目录结构
所有测试文件位于 `tests/` 目录，按功能模块组织。

## 核心测试文件

### 记忆系统测试
- [tests/test_memory_store.py](mdc:tests/test_memory_store.py) - 记忆存储测试
- [tests/test_vectorizer.py](mdc:tests/test_vectorizer.py) - 向量化测试
- [tests/test_embedding_cache.py](mdc:tests/test_embedding_cache.py) - 嵌入缓存测试
- [tests/test_faiss_retrieval.py](mdc:tests/test_faiss_retrieval.py) - FAISS检索测试
- [tests/test_association_network.py](mdc:tests/test_association_network.py) - 关联网络测试

### 数据库和索引测试
- [tests/test_db_init.py](mdc:tests/test_db_init.py) - 数据库初始化测试
- [tests/test_vector_index.py](mdc:tests/test_vector_index.py) - 向量索引测试

### 集成测试
- [tests/test_complete_workflow.py](mdc:tests/test_complete_workflow.py) - 完整工作流测试
- [tests/test_pipeline_integration.py](mdc:tests/test_pipeline_integration.py) - 管道集成测试

### 性能测试
- [tests/test_dialogue_performance.py](mdc:tests/test_dialogue_performance.py) - 对话性能测试
- [tests/test_cache_comparison.py](mdc:tests/test_cache_comparison.py) - 缓存性能对比

### 音频系统测试
- [tests/test_audio_system.py](mdc:tests/test_audio_system.py) - 音频系统测试
- [tests/test_tts.py](mdc:tests/test_tts.py) - TTS测试
- [tests/test_whisper.py](mdc:tests/test_whisper.py) - Whisper测试

## 测试策略
1. **单元测试** - 测试单个组件功能
2. **集成测试** - 测试组件间协作
3. **性能测试** - 测试系统性能指标
4. **端到端测试** - 测试完整用户场景

## 测试运行
- 使用pytest运行测试
- 测试前确保数据库和向量索引已初始化
- 配置测试环境变量和API密钥



