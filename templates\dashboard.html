
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estia 记忆监控仪表板</title>
    
    <!-- 引入Chart.js和其他依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.0.0/dist/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/wordcloud@1.2.2/src/wordcloud2.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2em;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #48bb78; }
        .status-idle { background: #a0aec0; }
        .status-error { background: #f56565; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child { border-bottom: none; }
        
        .metric-value {
            font-weight: bold;
            color: #2d3748;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        
        .wordcloud-container {
            height: 300px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .session-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .session-item {
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .session-id { 
            font-family: monospace;
            color: #6b7280;
            font-size: 0.9em;
        }
        
        .session-duration {
            color: #059669;
            font-weight: bold;
        }
        
        .session-query {
            color: #374151;
            font-style: italic;
            margin-bottom: 5px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(66,153,225,0.4);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background: #fef5e7;
            border-color: #f6ad55;
            color: #c53030;
        }
        
        .alert-info {
            background: #ebf8ff;
            border-color: #4299e1;
            color: #2b6cb0;
        }
        
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
            .header h1 { font-size: 2em; }
            .container { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Estia 记忆监控仪表板</h1>
        <p class="subtitle">13步记忆处理流程实时监控与分析</p>
    </div>
    
    <div class="container">
        <div class="grid">
            <!-- 实时状态卡片 -->
            <div class="card">
                <h3>📊 实时状态</h3>
                <div id="status-content">
                    <div class="metric">
                        <span>系统状态</span>
                        <span class="metric-value">
                            <span class="status-indicator status-idle"></span>
                            <span id="system-status">加载中...</span>
                        </span>
                    </div>
                    <div class="metric">
                        <span>当前会话</span>
                        <span class="metric-value" id="current-session">无</span>
                    </div>
                    <div class="metric">
                        <span>运行时间</span>
                        <span class="metric-value" id="runtime">0s</span>
                    </div>
                    <div class="metric">
                        <span>进度</span>
                        <span class="metric-value" id="progress">0%</span>
                    </div>
                </div>
            </div>
            
            <!-- 性能统计卡片 -->
            <div class="card">
                <h3>📈 性能统计</h3>
                <div id="performance-content">
                    <div class="metric">
                        <span>总会话数</span>
                        <span class="metric-value" id="total-sessions">0</span>
                    </div>
                    <div class="metric">
                        <span>平均耗时</span>
                        <span class="metric-value" id="avg-duration">0s</span>
                    </div>
                    <div class="metric">
                        <span>成功率</span>
                        <span class="metric-value" id="success-rate">0%</span>
                    </div>
                    <div class="metric">
                        <span>最慢步骤</span>
                        <span class="metric-value" id="slowest-step">无</span>
                    </div>
                </div>
            </div>
            
            <!-- 关键词云 -->
            <div class="card">
                <h3>☁️ 关键词云</h3>
                <div class="wordcloud-container" id="wordcloud"></div>
                <div id="keyword-stats" style="margin-top: 15px;">
                    <div class="metric">
                        <span>热门词汇</span>
                        <span class="metric-value" id="top-keywords">加载中...</span>
                    </div>
                </div>
            </div>
            
            <!-- 性能趋势图 -->
            <div class="card">
                <h3>📊 性能趋势</h3>
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>
            </div>
            
            <!-- 记忆分析 -->
            <div class="card">
                <h3>🧠 记忆分析</h3>
                <div id="memory-analysis">
                    <div class="metric">
                        <span>平均相似度</span>
                        <span class="metric-value" id="avg-similarity">0</span>
                    </div>
                    <div class="metric">
                        <span>检索次数</span>
                        <span class="metric-value" id="retrieval-count">0</span>
                    </div>
                    <div class="metric">
                        <span>关联拓展</span>
                        <span class="metric-value" id="associations-count">0</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="memory-chart"></canvas>
                </div>
            </div>
            
            <!-- 最近会话 -->
            <div class="card">
                <h3>💬 最近会话</h3>
                <div class="session-list" id="session-list">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshAllData()">
        🔄 刷新数据
    </button>
    
    <script>
        // WebSocket连接
        const socket = io();
        
        // 图表实例
        let performanceChart, memoryChart;
        
        // 初始化图表
        function initCharts() {
            // 性能趋势图
            const perfCtx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '会话耗时 (秒)',
                        data: [],
                        borderColor: '#4299e1',
                        backgroundColor: 'rgba(66,153,225,0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
            
            // 记忆分析图
            const memCtx = document.getElementById('memory-chart').getContext('2d');
            memoryChart = new Chart(memCtx, {
                type: 'doughnut',
                data: {
                    labels: ['高相似度', '中相似度', '低相似度'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: ['#48bb78', '#ed8936', '#f56565']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        // 更新状态显示
        function updateStatus(data) {
            const status = data.status;
            const summary = data.summary;
            
            // 更新系统状态
            const statusEl = document.getElementById('system-status');
            const indicatorEl = statusEl.previousElementSibling;
            
            if (status.status === 'running') {
                statusEl.textContent = '运行中';
                indicatorEl.className = 'status-indicator status-running';
            } else {
                statusEl.textContent = '空闲';
                indicatorEl.className = 'status-indicator status-idle';
            }
            
            // 更新其他信息
            document.getElementById('current-session').textContent = 
                status.session_id || '无';
            document.getElementById('runtime').textContent = 
                (status.running_time || 0).toFixed(2) + 's';
            document.getElementById('progress').textContent = 
                (status.progress_percentage || 0).toFixed(1) + '%';
            
            // 更新性能统计
            if (summary.total_sessions !== undefined) {
                document.getElementById('total-sessions').textContent = summary.total_sessions;
                document.getElementById('avg-duration').textContent = 
                    (summary.average_duration || 0).toFixed(3) + 's';
                document.getElementById('success-rate').textContent = 
                    ((summary.success_rate || 0) * 100).toFixed(1) + '%';
                
                const slowest = summary.slowest_step;
                document.getElementById('slowest-step').textContent = 
                    slowest && slowest.step ? 
                    `${slowest.step.split('_').pop()} (${(slowest.avg_duration || 0).toFixed(3)}s)` : '无';
            }
        }
        
        // 更新关键词云
        function updateWordCloud(keywords) {
            const container = document.getElementById('wordcloud');
            container.innerHTML = '';
            
            if (keywords && keywords.top_keywords && keywords.top_keywords.length > 0) {
                const wordList = keywords.top_keywords.map(item => [
                    item.word, Math.max(12, Math.min(40, item.count * 8))
                ]);
                
                WordCloud(container, {
                    list: wordList,
                    gridSize: 8,
                    weightFactor: 2,
                    fontFamily: 'Microsoft YaHei, sans-serif',
                    color: function() {
                        const colors = ['#4299e1', '#48bb78', '#ed8936', '#9f7aea', '#f56565'];
                        return colors[Math.floor(Math.random() * colors.length)];
                    },
                    rotateRatio: 0.2,
                    backgroundColor: '#f8f9fa'
                });
                
                // 更新热门关键词
                const topWords = keywords.top_keywords.slice(0, 3)
                    .map(item => item.word).join(', ');
                document.getElementById('top-keywords').textContent = topWords;
            } else {
                container.innerHTML = '<p style="text-align: center; margin-top: 100px; color: #a0aec0;">暂无关键词数据</p>';
                document.getElementById('top-keywords').textContent = '暂无数据';
            }
        }
        
        // 更新会话列表
        function updateSessionList(sessions) {
            const container = document.getElementById('session-list');
            
            if (!sessions || sessions.length === 0) {
                container.innerHTML = '<p>暂无会话数据</p>';
                return;
            }
            
            const html = sessions.map(session => `
                <div class="session-item">
                    <div class="session-header">
                        <span class="session-id">${session.session_id}</span>
                        <span class="session-duration">${session.duration.toFixed(3)}s</span>
                    </div>
                    <div class="session-query">"${session.user_input}"</div>
                    <div style="font-size: 0.9em; color: #6b7280;">
                        成功: ${session.success_count} | 失败: ${session.failed_count}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 刷新所有数据
        async function refreshAllData() {
            try {
                // 获取状态数据
                const statusRes = await fetch('/api/status');
                const statusData = await statusRes.json();
                updateStatus(statusData);
                
                // 获取关键词数据
                const keywordRes = await fetch('/api/keywords');
                if (keywordRes.ok) {
                    const keywordData = await keywordRes.json();
                    if (keywordData.keywords) {
                        updateWordCloud(keywordData.keywords);
                    }
                }
                
                // 获取会话数据
                const sessionRes = await fetch('/api/sessions');
                if (sessionRes.ok) {
                    const sessionData = await sessionRes.json();
                    updateSessionList(sessionData.sessions);
                    
                    // 更新性能图表
                    if (sessionData.sessions.length > 0) {
                        const labels = sessionData.sessions.map((_, i) => `会话${i+1}`);
                        const durations = sessionData.sessions.map(s => s.duration);
                        
                        performanceChart.data.labels = labels.slice(-10); // 最近10个
                        performanceChart.data.datasets[0].data = durations.slice(-10);
                        performanceChart.update();
                    }
                }
                
                // 获取记忆分析数据
                const memoryRes = await fetch('/api/memory_analysis');
                if (memoryRes.ok) {
                    const memoryData = await memoryRes.json();
                    if (memoryData.memory) {
                        const memory = memoryData.memory;
                        document.getElementById('avg-similarity').textContent = 
                            (memory.average_similarity || 0).toFixed(3);
                        document.getElementById('retrieval-count').textContent = 
                            memory.total_retrievals || 0;
                        document.getElementById('associations-count').textContent = 
                            memory.memory_usage_stats?.associations || 0;
                        
                        // 更新相似度分布图
                        if (memory.similarity_distribution) {
                            const dist = memory.similarity_distribution;
                            memoryChart.data.datasets[0].data = [
                                dist['高 (>0.8)'] || 0,
                                dist['中 (0.6-0.8)'] || 0,
                                dist['低 (<0.6)'] || 0
                            ];
                            memoryChart.update();
                        }
                    }
                }
                
                // 获取v6.0综合统计数据
                try {
                    const v6StatsRes = await fetch('/api/v6_comprehensive_stats');
                    if (v6StatsRes.ok) {
                        const v6Data = await v6StatsRes.json();
                        if (v6Data.v6_stats && !v6Data.v6_stats.error) {
                            // 更新v6.0性能指标显示
                            updateV6Performance(v6Data.v6_stats);
                        }
                    }
                } catch (error) {
                    console.log('v6.0统计数据获取失败:', error);
                }
                
                // 获取13步流程监控数据
                try {
                    const stepRes = await fetch('/api/13_step_monitoring');
                    if (stepRes.ok) {
                        const stepData = await stepRes.json();
                        if (stepData['13_step_data'] && !stepData['13_step_data'].error) {
                            // 更新13步流程显示
                            update13StepMonitoring(stepData['13_step_data']);
                        }
                    }
                } catch (error) {
                    console.log('13步监控数据获取失败:', error);
                }
                
            } catch (error) {
                console.error('刷新数据失败:', error);
                // 显示错误提示
                showErrorNotification('数据刷新失败，请检查网络连接');
            }
        }
        
        // WebSocket事件处理
        socket.on('connect', function() {
            console.log('连接到监控服务器');
            socket.emit('start_monitoring');
        });
        
        socket.on('status_update', function(data) {
            updateStatus(data);
        });
        
        // 更新v6.0性能指标
        function updateV6Performance(v6Stats) {
            if (v6Stats.performance_summary) {
                const perf = v6Stats.performance_summary;
                
                // 更新缓存命中率显示
                if (perf.cache_hit_rate !== undefined) {
                    console.log(`v6.0缓存命中率: ${(perf.cache_hit_rate * 100).toFixed(1)}%`);
                }
                
                // 更新系统健康状态
                if (perf.system_health) {
                    const healthIndicator = document.querySelector('.status-indicator');
                    if (perf.system_health === 'healthy') {
                        healthIndicator.className = 'status-indicator status-running';
                    } else if (perf.system_health === 'unhealthy') {
                        healthIndicator.className = 'status-indicator status-error';
                    }
                }
            }
        }
        
        // 更新13步流程监控
        function update13StepMonitoring(stepData) {
            if (stepData.total_steps) {
                console.log(`13步流程监控: 总步骤${stepData.total_steps}, 同步${stepData.sync_steps}, 异步${stepData.async_steps}`);
                
                // 可以在这里添加更多的13步流程可视化
                if (stepData.step_performance) {
                    console.log('步骤性能:', stepData.step_performance);
                }
            }
        }
        
        // 显示错误通知
        function showErrorNotification(message) {
            // 创建错误通知元素
            const notification = document.createElement('div');
            notification.className = 'alert alert-warning';
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.maxWidth = '300px';
            notification.innerHTML = `
                <strong>⚠️ 错误</strong><br>
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            `;
            
            document.body.appendChild(notification);
            
            // 5秒后自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // 优化数据刷新频率
        let refreshInterval = 5000; // 默认5秒
        let isRefreshing = false;
        
        // 智能刷新函数
        async function smartRefresh() {
            if (isRefreshing) return;
            
            isRefreshing = true;
            try {
                await refreshAllData();
                // 如果成功，保持正常频率
                refreshInterval = 5000;
            } catch (error) {
                // 如果失败，降低刷新频率
                refreshInterval = Math.min(refreshInterval * 1.5, 30000); // 最多30秒
                console.warn(`刷新失败，调整频率为${refreshInterval/1000}秒`);
            } finally {
                isRefreshing = false;
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            refreshAllData();
            
            // 使用智能刷新
            setInterval(smartRefresh, 5000);
            
            // 页面可见性变化时调整刷新频率
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    // 页面隐藏时降低刷新频率
                    refreshInterval = 15000;
                } else {
                    // 页面显示时恢复正常频率
                    refreshInterval = 5000;
                    refreshAllData(); // 立即刷新一次
                }
            });
        });
    </script>
</body>
</html>
    