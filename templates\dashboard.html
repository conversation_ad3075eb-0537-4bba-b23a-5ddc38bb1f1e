
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estia 记忆监控仪表板</title>
    
    <!-- 引入Chart.js和其他依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.0.0/dist/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/wordcloud@1.2.2/src/wordcloud2.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2em;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #48bb78; }
        .status-idle { background: #a0aec0; }
        .status-error { background: #f56565; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child { border-bottom: none; }
        
        .metric-value {
            font-weight: bold;
            color: #2d3748;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        
        .wordcloud-container {
            height: 300px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .session-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .session-item {
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .session-id { 
            font-family: monospace;
            color: #6b7280;
            font-size: 0.9em;
        }
        
        .session-duration {
            color: #059669;
            font-weight: bold;
        }
        
        .session-query {
            color: #374151;
            font-style: italic;
            margin-bottom: 5px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(66,153,225,0.4);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background: #fef5e7;
            border-color: #f6ad55;
            color: #c53030;
        }
        
        .alert-info {
            background: #ebf8ff;
            border-color: #4299e1;
            color: #2b6cb0;
        }
        
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
            .header h1 { font-size: 2em; }
            .container { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Estia 记忆监控仪表板</h1>
        <p class="subtitle">13步记忆处理流程实时监控与分析</p>
    </div>
    
    <div class="container">
        <div class="grid">
            <!-- 实时状态卡片 -->
            <div class="card">
                <h3>📊 实时状态</h3>
                <div id="status-content">
                    <div class="metric">
                        <span>系统状态</span>
                        <span class="metric-value">
                            <span class="status-indicator status-idle"></span>
                            <span id="system-status">加载中...</span>
                        </span>
                    </div>
                    <div class="metric">
                        <span>当前会话</span>
                        <span class="metric-value" id="current-session">无</span>
                    </div>
                    <div class="metric">
                        <span>运行时间</span>
                        <span class="metric-value" id="runtime">0s</span>
                    </div>
                    <div class="metric">
                        <span>进度</span>
                        <span class="metric-value" id="progress">0%</span>
                    </div>
                </div>
            </div>
            
            <!-- 性能统计卡片 -->
            <div class="card">
                <h3>📈 性能统计</h3>
                <div id="performance-content">
                    <div class="metric">
                        <span>总会话数</span>
                        <span class="metric-value" id="total-sessions">0</span>
                    </div>
                    <div class="metric">
                        <span>平均耗时</span>
                        <span class="metric-value" id="avg-duration">0s</span>
                    </div>
                    <div class="metric">
                        <span>成功率</span>
                        <span class="metric-value" id="success-rate">0%</span>
                    </div>
                    <div class="metric">
                        <span>最慢步骤</span>
                        <span class="metric-value" id="slowest-step">无</span>
                    </div>
                </div>
            </div>
            
            <!-- 关键词云 -->
            <div class="card">
                <h3>☁️ 关键词云</h3>
                <div class="wordcloud-container" id="wordcloud"></div>
                <div id="keyword-stats" style="margin-top: 15px;">
                    <div class="metric">
                        <span>热门词汇</span>
                        <span class="metric-value" id="top-keywords">加载中...</span>
                    </div>
                </div>
            </div>
            
            <!-- 性能趋势图 -->
            <div class="card">
                <h3>📊 性能趋势</h3>
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>
            </div>
            
            <!-- 记忆分析 -->
            <div class="card">
                <h3>🧠 记忆分析</h3>
                <div id="memory-analysis">
                    <div class="metric">
                        <span>平均相似度</span>
                        <span class="metric-value" id="avg-similarity">0</span>
                    </div>
                    <div class="metric">
                        <span>检索次数</span>
                        <span class="metric-value" id="retrieval-count">0</span>
                    </div>
                    <div class="metric">
                        <span>关联拓展</span>
                        <span class="metric-value" id="associations-count">0</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="memory-chart"></canvas>
                </div>
            </div>
            
            <!-- 最近会话 -->
            <div class="card">
                <h3>💬 最近会话</h3>
                <div class="session-list" id="session-list">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="window.manualRefresh()">
        🔄 刷新数据
    </button>

    <button class="refresh-btn" onclick="window.loadTestData()" style="bottom: 80px; background: #48bb78;">
        🧪 加载测试数据
    </button>
    
    <script>
        // 全局变量声明
        let refreshAllData, showInfoNotification, showSuccessNotification, showErrorNotification;
        let updateStatus, updateWordCloud, updateSessionList, updatePerformanceChart, updateMemoryAnalysis;

        // 全局手动刷新函数（立即定义）
        window.manualRefresh = async function() {
            try {
                if (showInfoNotification) showInfoNotification('正在刷新数据...');
                if (refreshAllData) {
                    await refreshAllData();
                    if (showSuccessNotification) showSuccessNotification('数据刷新成功！');
                } else {
                    console.warn('refreshAllData函数尚未初始化');
                }
            } catch (error) {
                console.error('手动刷新失败:', error);
                if (showErrorNotification) showErrorNotification('数据刷新失败: ' + error.message);
            }
        };

        // 加载测试数据函数（立即定义）
        window.loadTestData = async function() {
            try {
                if (showInfoNotification) showInfoNotification('正在加载测试数据...');

                const response = await fetch('/api/generate_test_data');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                // 更新所有组件
                if (data.status && updateStatus) {
                    updateStatus(data.status);
                }

                if (data.keywords && updateWordCloud) {
                    updateWordCloud(data.keywords);
                }

                if (data.sessions && data.sessions.sessions && updateSessionList) {
                    updateSessionList(data.sessions.sessions);

                    // 更新性能图表
                    if (updatePerformanceChart) {
                        const sessions = data.sessions.sessions;
                        const labels = sessions.map((_, i) => `会话${i+1}`);
                        const durations = sessions.map(s => s.duration || 0);
                        updatePerformanceChart(labels, durations);
                    }
                }

                if (data.memory && updateMemoryAnalysis) {
                    updateMemoryAnalysis(data.memory);
                }

                if (showSuccessNotification) showSuccessNotification('测试数据加载成功！现在可以看到界面效果了。');

            } catch (error) {
                console.error('加载测试数据失败:', error);
                if (showErrorNotification) showErrorNotification('测试数据加载失败: ' + error.message);
            }
        };

        // WebSocket连接
        const socket = io();

        // 图表实例
        let performanceChart, memoryChart;

        // 性能优化相关变量
        let dataCache = new Map();
        let lastUpdateTime = 0;
        let isUpdating = false;
        let updateQueue = [];
        let refreshInterval = 8000; // 默认8秒刷新间隔

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }
        
        // 初始化图表（优化版）
        function initCharts() {
            // 性能趋势图
            const perfCtx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '会话耗时 (秒)',
                        data: [],
                        borderColor: '#4299e1',
                        backgroundColor: 'rgba(66,153,225,0.1)',
                        tension: 0.4,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 300 // 减少动画时间
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                maxTicksLimit: 6 // 限制刻度数量
                            }
                        },
                        x: {
                            ticks: {
                                maxTicksLimit: 10 // 限制X轴标签数量
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false // 隐藏图例减少渲染
                        }
                    }
                }
            });

            // 记忆分析图
            const memCtx = document.getElementById('memory-chart').getContext('2d');
            memoryChart = new Chart(memCtx, {
                type: 'doughnut',
                data: {
                    labels: ['高相似度', '中相似度', '低相似度'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: ['#48bb78', '#ed8936', '#f56565'],
                        borderWidth: 0 // 移除边框减少渲染
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 300 // 减少动画时间
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 10
                            }
                        }
                    }
                }
            });
        }

        // 优化的图表更新函数
        updatePerformanceChart = throttle(function(labels, data) {
            if (!performanceChart || !labels || !data) return;

            // 只在数据真正变化时更新
            const currentData = performanceChart.data.datasets[0].data;
            if (JSON.stringify(currentData) === JSON.stringify(data)) return;

            performanceChart.data.labels = labels;
            performanceChart.data.datasets[0].data = data;
            performanceChart.update('none'); // 无动画更新
        }, 1000);

        const updateMemoryChart = throttle(function(data) {
            if (!memoryChart || !data) return;

            // 只在数据真正变化时更新
            const currentData = memoryChart.data.datasets[0].data;
            if (JSON.stringify(currentData) === JSON.stringify(data)) return;

            memoryChart.data.datasets[0].data = data;
            memoryChart.update('none'); // 无动画更新
        }, 1000);
        
        // 优化的状态更新函数
        updateStatus = function(data) {
            if (!data || !data.status) return;

            const status = data.status;
            const summary = data.summary || {};

            // 批量更新DOM，减少重排重绘
            const updates = [];

            // 更新系统状态
            const statusEl = document.getElementById('system-status');
            const indicatorEl = statusEl?.previousElementSibling;

            if (statusEl && indicatorEl) {
                if (status.status === 'running') {
                    updates.push(() => {
                        statusEl.textContent = '运行中';
                        indicatorEl.className = 'status-indicator status-running';
                    });
                } else {
                    updates.push(() => {
                        statusEl.textContent = '空闲';
                        indicatorEl.className = 'status-indicator status-idle';
                    });
                }
            }

            // 准备其他更新
            const elementUpdates = [
                ['current-session', status.session_id || '无'],
                ['runtime', (status.running_time || 0).toFixed(2) + 's'],
                ['progress', (status.progress_percentage || 0).toFixed(1) + '%']
            ];

            if (summary.total_sessions !== undefined) {
                elementUpdates.push(
                    ['total-sessions', summary.total_sessions],
                    ['avg-duration', (summary.average_duration || 0).toFixed(3) + 's'],
                    ['success-rate', ((summary.success_rate || 0) * 100).toFixed(1) + '%']
                );

                const slowest = summary.slowest_step;
                const slowestText = slowest && slowest.step ?
                    `${slowest.step.split('_').pop()} (${(slowest.avg_duration || 0).toFixed(3)}s)` : '无';
                elementUpdates.push(['slowest-step', slowestText]);
            }

            // 批量执行DOM更新
            requestAnimationFrame(() => {
                updates.forEach(update => update());

                elementUpdates.forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element && element.textContent !== String(value)) {
                        element.textContent = value;
                    }
                });
            });
        }
        
        // 优化的词云更新函数
        updateWordCloud = debounce(function(keywords) {
            const container = document.getElementById('wordcloud');
            if (!container) return;

            // 检查数据是否真的变化了
            const cacheKey = 'wordcloud_data';
            const cachedData = dataCache.get(cacheKey);
            const currentData = JSON.stringify(keywords);

            if (cachedData === currentData) return;
            dataCache.set(cacheKey, currentData);

            // 使用requestAnimationFrame优化渲染
            requestAnimationFrame(() => {
                if (keywords && keywords.top_keywords && keywords.top_keywords.length > 0) {
                    const wordList = keywords.top_keywords.map(item => [
                        item.word, Math.max(12, Math.min(40, item.count * 8))
                    ]);

                    // 清空容器
                    container.innerHTML = '';

                    try {
                        WordCloud(container, {
                            list: wordList,
                            gridSize: 10, // 增大网格减少计算
                            weightFactor: 1.5, // 减小权重因子
                            fontFamily: 'Microsoft YaHei, sans-serif',
                            color: function() {
                                const colors = ['#4299e1', '#48bb78', '#ed8936', '#9f7aea', '#f56565'];
                                return colors[Math.floor(Math.random() * colors.length)];
                            },
                            rotateRatio: 0.1, // 减少旋转减少计算
                            backgroundColor: '#f8f9fa',
                            minSize: 12, // 设置最小字体
                            drawOutOfBound: false // 不绘制边界外的词
                        });

                        // 更新热门关键词
                        const topWords = keywords.top_keywords.slice(0, 3)
                            .map(item => item.word).join(', ');
                        const topKeywordsEl = document.getElementById('top-keywords');
                        if (topKeywordsEl) {
                            topKeywordsEl.textContent = topWords;
                        }
                    } catch (error) {
                        console.warn('词云生成失败:', error);
                        container.innerHTML = '<p style="text-align: center; margin-top: 100px; color: #f56565;">词云生成失败</p>';
                    }
                } else {
                    container.innerHTML = '<p style="text-align: center; margin-top: 100px; color: #a0aec0;">暂无关键词数据</p>';
                    const topKeywordsEl = document.getElementById('top-keywords');
                    if (topKeywordsEl) {
                        topKeywordsEl.textContent = '暂无数据';
                    }
                }
            });
        }, 2000); // 2秒防抖
        
        // 优化的会话列表更新函数
        updateSessionList = throttle(function(sessions) {
            const container = document.getElementById('session-list');
            if (!container) return;

            // 检查数据是否变化
            const cacheKey = 'session_list_data';
            const cachedData = dataCache.get(cacheKey);
            const currentData = JSON.stringify(sessions);

            if (cachedData === currentData) return;
            dataCache.set(cacheKey, currentData);

            requestAnimationFrame(() => {
                if (!sessions || sessions.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #a0aec0; padding: 20px;">暂无会话数据</p>';
                    return;
                }

                // 使用DocumentFragment减少DOM操作
                const fragment = document.createDocumentFragment();

                sessions.forEach(session => {
                    const sessionDiv = document.createElement('div');
                    sessionDiv.className = 'session-item';

                    // 安全地处理数据
                    const sessionId = session.session_id || 'unknown';
                    const duration = (session.duration || 0).toFixed(3);
                    const userInput = (session.user_input || '').substring(0, 100);
                    const successCount = session.success_count || 0;
                    const failedCount = session.failed_count || 0;

                    sessionDiv.innerHTML = `
                        <div class="session-header">
                            <span class="session-id">${sessionId}</span>
                            <span class="session-duration">${duration}s</span>
                        </div>
                        <div class="session-query">"${userInput}"</div>
                        <div style="font-size: 0.9em; color: #6b7280;">
                            成功: ${successCount} | 失败: ${failedCount}
                        </div>
                    `;

                    fragment.appendChild(sessionDiv);
                });

                // 一次性更新DOM
                container.innerHTML = '';
                container.appendChild(fragment);
            });
        }, 1500); // 1.5秒节流
        
        // 优化的批量数据刷新函数
        refreshAllData = async function() {
            if (isUpdating) {
                console.log('正在更新中，跳过本次刷新');
                return;
            }

            isUpdating = true;
            const startTime = performance.now();

            try {
                // 使用新的批量API减少请求数量
                const response = await fetch('/api/dashboard_data');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 即使有错误也尝试显示可用的数据
                if (data.error && !data.has_data) {
                    console.warn('服务器返回错误:', data.error);
                    // 如果是"暂无数据"错误，显示空状态而不是错误
                    if (data.error.includes('暂无数据')) {
                        showInfoNotification('暂无监控数据，可以点击"加载测试数据"查看界面效果');
                    } else {
                        showErrorNotification(data.error);
                    }
                    // 继续处理，显示空状态
                }

                // 批量更新所有组件
                const updatePromises = [];

                // 更新状态
                if (data.status) {
                    updatePromises.push(Promise.resolve(updateStatus(data.status)));
                }

                // 更新关键词云
                if (data.keywords) {
                    updatePromises.push(Promise.resolve(updateWordCloud(data.keywords)));
                }

                // 更新会话列表和性能图表
                if (data.sessions && data.sessions.sessions) {
                    updatePromises.push(Promise.resolve(updateSessionList(data.sessions.sessions)));

                    // 更新性能图表
                    if (data.sessions.sessions.length > 0) {
                        const sessions = data.sessions.sessions;
                        const labels = sessions.map((_, i) => `会话${i+1}`);
                        const durations = sessions.map(s => s.duration || 0);

                        updatePromises.push(Promise.resolve(
                            updatePerformanceChart(labels.slice(-10), durations.slice(-10))
                        ));
                    }
                }

                // 更新记忆分析
                if (data.memory) {
                    updatePromises.push(Promise.resolve(updateMemoryAnalysis(data.memory)));
                }

                // 等待所有更新完成
                await Promise.all(updatePromises);

                // 记录性能
                const endTime = performance.now();
                console.log(`数据刷新完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

                // 动态调整刷新间隔
                const updateTime = endTime - startTime;
                if (updateTime > 1000) {
                    refreshInterval = Math.min(refreshInterval * 1.2, 15000); // 最多15秒
                } else if (updateTime < 200) {
                    refreshInterval = Math.max(refreshInterval * 0.9, 5000); // 最少5秒
                }

            } catch (error) {
                console.error('批量数据刷新失败:', error);
                showErrorNotification('数据刷新失败: ' + error.message);

                // 增加刷新间隔
                refreshInterval = Math.min(refreshInterval * 1.5, 30000);

            } finally {
                isUpdating = false;
                lastUpdateTime = Date.now();
            }
        }

        // 记忆分析更新函数
        updateMemoryAnalysis = function(memory) {
            if (!memory) return;

            // 批量更新记忆分析指标
            const updates = [
                ['avg-similarity', (memory.average_similarity || 0).toFixed(3)],
                ['retrieval-count', memory.total_retrievals || 0],
                ['associations-count', memory.memory_usage_stats?.associations || 0]
            ];

            requestAnimationFrame(() => {
                updates.forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element && element.textContent !== String(value)) {
                        element.textContent = value;
                    }
                });
            });

            // 更新相似度分布图
            if (memory.similarity_distribution) {
                const dist = memory.similarity_distribution;
                const chartData = [
                    dist['高 (>0.8)'] || 0,
                    dist['中 (0.6-0.8)'] || 0,
                    dist['低 (<0.6)'] || 0
                ];
                updateMemoryChart(chartData);
            }
        }
                        
                        // 更新相似度分布图
                        if (memory.similarity_distribution) {
                            const dist = memory.similarity_distribution;
                            memoryChart.data.datasets[0].data = [
                                dist['高 (>0.8)'] || 0,
                                dist['中 (0.6-0.8)'] || 0,
                                dist['低 (<0.6)'] || 0
                            ];
                            memoryChart.update();
                        }
                    }
                }
                
                // 获取v6.0综合统计数据
                try {
                    const v6StatsRes = await fetch('/api/v6_comprehensive_stats');
                    if (v6StatsRes.ok) {
                        const v6Data = await v6StatsRes.json();
                        if (v6Data.v6_stats && !v6Data.v6_stats.error) {
                            // 更新v6.0性能指标显示
                            updateV6Performance(v6Data.v6_stats);
                        }
                    }
                } catch (error) {
                    console.log('v6.0统计数据获取失败:', error);
                }
                
                // 获取13步流程监控数据
                try {
                    const stepRes = await fetch('/api/13_step_monitoring');
                    if (stepRes.ok) {
                        const stepData = await stepRes.json();
                        if (stepData['13_step_data'] && !stepData['13_step_data'].error) {
                            // 更新13步流程显示
                            update13StepMonitoring(stepData['13_step_data']);
                        }
                    }
                } catch (error) {
                    console.log('13步监控数据获取失败:', error);
                }
                
            } catch (error) {
                console.error('刷新数据失败:', error);
                // 显示错误提示
                showErrorNotification('数据刷新失败，请检查网络连接');
            }
        }
        
        // WebSocket事件处理（优化版）
        socket.on('connect', function() {
            console.log('✅ 连接到监控服务器');
            socket.emit('start_monitoring');

            // 连接成功后显示通知
            showSuccessNotification('实时监控连接已建立');
        });

        socket.on('disconnect', function() {
            console.log('❌ 与监控服务器断开连接');
            showErrorNotification('实时监控连接已断开');
        });

        socket.on('status_update', function(data) {
            try {
                updateStatus(data);
            } catch (error) {
                console.error('状态更新失败:', error);
            }
        });

        socket.on('monitoring_error', function(data) {
            console.error('监控错误:', data);
            showErrorNotification('监控服务出现错误: ' + data.error);
        });

        // 连接错误处理
        socket.on('connect_error', function(error) {
            console.error('WebSocket连接错误:', error);
            showErrorNotification('无法连接到监控服务器');
        });
        
        // 更新v6.0性能指标
        function updateV6Performance(v6Stats) {
            if (v6Stats.performance_summary) {
                const perf = v6Stats.performance_summary;
                
                // 更新缓存命中率显示
                if (perf.cache_hit_rate !== undefined) {
                    console.log(`v6.0缓存命中率: ${(perf.cache_hit_rate * 100).toFixed(1)}%`);
                }
                
                // 更新系统健康状态
                if (perf.system_health) {
                    const healthIndicator = document.querySelector('.status-indicator');
                    if (perf.system_health === 'healthy') {
                        healthIndicator.className = 'status-indicator status-running';
                    } else if (perf.system_health === 'unhealthy') {
                        healthIndicator.className = 'status-indicator status-error';
                    }
                }
            }
        }
        
        // 更新13步流程监控
        function update13StepMonitoring(stepData) {
            if (stepData.total_steps) {
                console.log(`13步流程监控: 总步骤${stepData.total_steps}, 同步${stepData.sync_steps}, 异步${stepData.async_steps}`);
                
                // 可以在这里添加更多的13步流程可视化
                if (stepData.step_performance) {
                    console.log('步骤性能:', stepData.step_performance);
                }
            }
        }
        
        // 通知系统（优化版）
        function showNotification(message, type = 'info', duration = 5000) {
            // 避免重复通知
            const existingNotifications = document.querySelectorAll('.notification');
            for (const existing of existingNotifications) {
                if (existing.textContent.includes(message.substring(0, 20))) {
                    return; // 相似通知已存在
                }
            }

            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 350px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                animation: slideIn 0.3s ease-out;
            `;

            const icons = {
                'warning': '⚠️',
                'info': 'ℹ️',
                'success': '✅'
            };

            notification.innerHTML = `
                <strong>${icons[type] || 'ℹ️'} ${type === 'warning' ? '错误' : type === 'success' ? '成功' : '信息'}</strong><br>
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer; color: inherit;">&times;</button>
            `;

            document.body.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        }

        showErrorNotification = function(message) {
            showNotification(message, 'warning');
        };

        showSuccessNotification = function(message) {
            showNotification(message, 'success', 3000);
        };

        showInfoNotification = function(message) {
            showNotification(message, 'info', 4000);
        };

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        // 智能刷新管理器
        class SmartRefreshManager {
            constructor() {
                this.refreshTimer = null;
                this.isPageVisible = !document.hidden;
                this.consecutiveErrors = 0;
                this.maxErrors = 3;
            }

            start() {
                this.stop(); // 确保没有重复的定时器
                this.scheduleNext();
            }

            stop() {
                if (this.refreshTimer) {
                    clearTimeout(this.refreshTimer);
                    this.refreshTimer = null;
                }
            }

            scheduleNext() {
                const interval = this.calculateInterval();
                this.refreshTimer = setTimeout(async () => {
                    try {
                        await refreshAllData();
                        this.consecutiveErrors = 0; // 重置错误计数
                    } catch (error) {
                        this.consecutiveErrors++;
                        console.warn(`刷新失败 (${this.consecutiveErrors}/${this.maxErrors}):`, error);

                        if (this.consecutiveErrors >= this.maxErrors) {
                            console.error('连续刷新失败过多，暂停自动刷新');
                            return; // 停止自动刷新
                        }
                    }

                    this.scheduleNext(); // 安排下次刷新
                }, interval);
            }

            calculateInterval() {
                let baseInterval = 8000; // 基础8秒

                // 根据页面可见性调整
                if (!this.isPageVisible) {
                    baseInterval *= 2; // 页面隐藏时降低频率
                }

                // 根据错误次数调整
                if (this.consecutiveErrors > 0) {
                    baseInterval *= Math.pow(1.5, this.consecutiveErrors);
                }

                return Math.min(baseInterval, 30000); // 最多30秒
            }

            onVisibilityChange() {
                this.isPageVisible = !document.hidden;

                if (this.isPageVisible) {
                    // 页面变为可见时立即刷新
                    refreshAllData().catch(console.error);
                }
            }
        }

        const refreshManager = new SmartRefreshManager();

        // 性能监控器
        class PerformanceMonitor {
            constructor() {
                this.metrics = {
                    pageLoadTime: 0,
                    apiCallTimes: [],
                    renderTimes: [],
                    errorCount: 0
                };
                this.startTime = performance.now();
            }

            recordApiCall(duration) {
                this.metrics.apiCallTimes.push(duration);
                // 只保留最近50次记录
                if (this.metrics.apiCallTimes.length > 50) {
                    this.metrics.apiCallTimes.shift();
                }
            }

            recordRenderTime(duration) {
                this.metrics.renderTimes.push(duration);
                if (this.metrics.renderTimes.length > 50) {
                    this.metrics.renderTimes.shift();
                }
            }

            recordError() {
                this.metrics.errorCount++;
            }

            getAverageApiTime() {
                const times = this.metrics.apiCallTimes;
                return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
            }

            getAverageRenderTime() {
                const times = this.metrics.renderTimes;
                return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
            }

            getReport() {
                return {
                    uptime: (performance.now() - this.startTime) / 1000,
                    avgApiTime: this.getAverageApiTime(),
                    avgRenderTime: this.getAverageRenderTime(),
                    errorCount: this.metrics.errorCount,
                    totalApiCalls: this.metrics.apiCallTimes.length,
                    totalRenders: this.metrics.renderTimes.length
                };
            }
        }

        const perfMonitor = new PerformanceMonitor();

        // 重写fetch以监控API性能
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
            const startTime = performance.now();
            try {
                const response = await originalFetch.apply(this, args);
                const duration = performance.now() - startTime;
                perfMonitor.recordApiCall(duration);
                return response;
            } catch (error) {
                perfMonitor.recordError();
                throw error;
            }
        };

        // 定期输出性能报告
        setInterval(() => {
            const report = perfMonitor.getReport();
            console.log('📊 性能报告:', {
                运行时间: `${report.uptime.toFixed(1)}秒`,
                平均API耗时: `${report.avgApiTime.toFixed(1)}ms`,
                平均渲染耗时: `${report.avgRenderTime.toFixed(1)}ms`,
                错误次数: report.errorCount,
                API调用次数: report.totalApiCalls
            });
        }, 60000); // 每分钟输出一次

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化仪表板...');

            // 初始化图表
            initCharts();

            // 首次加载数据
            refreshAllData().then(() => {
                console.log('初始数据加载完成');
                // 启动智能刷新
                refreshManager.start();
            }).catch(error => {
                console.error('初始数据加载失败:', error);
                showErrorNotification('初始数据加载失败');
                // 即使失败也启动刷新管理器
                refreshManager.start();
            });

            // 页面可见性变化监听
            document.addEventListener('visibilitychange', () => {
                refreshManager.onVisibilityChange();
            });

            // 页面卸载时清理
            window.addEventListener('beforeunload', () => {
                refreshManager.stop();
            });
        });
    </script>
</body>
</html>
    