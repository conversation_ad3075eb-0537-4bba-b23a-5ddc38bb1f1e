<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estia 记忆监控仪表板 - 简化版</title>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child { border-bottom: none; }
        
        .metric-value {
            font-weight: bold;
            color: #2d3748;
        }
        
        .btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(66,153,225,0.4);
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        .btn-test {
            background: #48bb78;
            bottom: 80px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
        }
        
        .notification.success {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            color: #2f855a;
        }
        
        .notification.error {
            background: #fef5e7;
            border-left: 4px solid #f56565;
            color: #c53030;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Estia 记忆监控仪表板</h1>
        <p style="color: rgba(255,255,255,0.8);">简化测试版</p>
    </div>
    
    <div class="container">
        <div class="grid">
            <!-- 实时状态卡片 -->
            <div class="card">
                <h3>📊 实时状态</h3>
                <div id="status-content">
                    <div class="metric">
                        <span>系统状态</span>
                        <span class="metric-value" id="system-status">加载中...</span>
                    </div>
                    <div class="metric">
                        <span>总会话数</span>
                        <span class="metric-value" id="total-sessions">0</span>
                    </div>
                    <div class="metric">
                        <span>平均耗时</span>
                        <span class="metric-value" id="avg-duration">0s</span>
                    </div>
                    <div class="metric">
                        <span>成功率</span>
                        <span class="metric-value" id="success-rate">0%</span>
                    </div>
                </div>
            </div>
            
            <!-- 会话列表 -->
            <div class="card">
                <h3>💬 最近会话</h3>
                <div id="session-list">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
    <button class="btn btn-test" onclick="loadTestData()">🧪 测试数据</button>
    
    <script>
        // 简化的通知函数
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // 更新状态显示
        function updateStatus(data) {
            if (data.status && data.summary) {
                document.getElementById('system-status').textContent = '正常';
                document.getElementById('total-sessions').textContent = data.summary.total_sessions || 0;
                document.getElementById('avg-duration').textContent = (data.summary.average_duration || 0).toFixed(3) + 's';
                document.getElementById('success-rate').textContent = ((data.summary.success_rate || 0) * 100).toFixed(1) + '%';
            }
        }
        
        // 更新会话列表
        function updateSessionList(sessions) {
            const container = document.getElementById('session-list');
            
            if (!sessions || sessions.length === 0) {
                container.innerHTML = '<p>暂无会话数据</p>';
                return;
            }
            
            const html = sessions.map(session => `
                <div style="padding: 10px; border: 1px solid #e2e8f0; border-radius: 8px; margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span style="font-family: monospace; color: #6b7280;">${session.session_id}</span>
                        <span style="color: #059669; font-weight: bold;">${session.duration.toFixed(3)}s</span>
                    </div>
                    <div style="color: #374151; font-style: italic;">"${session.user_input}"</div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 刷新数据
        async function refreshData() {
            try {
                showNotification('正在刷新数据...', 'success');
                
                const response = await fetch('/api/dashboard_data');
                const data = await response.json();
                
                if (data.error) {
                    showNotification('暂无数据，请点击"测试数据"按钮', 'error');
                    return;
                }
                
                if (data.status) {
                    updateStatus(data.status);
                }
                
                if (data.sessions && data.sessions.sessions) {
                    updateSessionList(data.sessions.sessions);
                }
                
                showNotification('数据刷新成功！', 'success');
                
            } catch (error) {
                console.error('刷新失败:', error);
                showNotification('刷新失败: ' + error.message, 'error');
            }
        }
        
        // 加载测试数据
        async function loadTestData() {
            try {
                showNotification('正在加载测试数据...', 'success');
                
                const response = await fetch('/api/generate_test_data');
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                if (data.status) {
                    updateStatus(data.status);
                }
                
                if (data.sessions && data.sessions.sessions) {
                    updateSessionList(data.sessions.sessions);
                }
                
                showNotification('测试数据加载成功！', 'success');
                
            } catch (error) {
                console.error('加载测试数据失败:', error);
                showNotification('测试数据加载失败: ' + error.message, 'error');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简化版仪表板初始化完成');
            refreshData();
        });
    </script>
</body>
</html>
