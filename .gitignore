# 这是一个为 Estia-AI 助手项目量身定制的 .gitignore 文件

# 忽略所有 Python 缓存文件
__pycache__/
*.pyc
*.pyo
*.pyd

# 忽略 Conda 和 venv 虚拟环境
# 特别是 Oobabooga 的专属环境
installer_files/env/


# 忽略我们存放AI大脑的独立服务器文件夹
# 假设它和你的 estia 项目文件夹在同一个父目录下
# 如果不在，可以忽略这一行，或者修改路径
llama/

# 忽略我们自己的项目生成的数据
# 比如录音和AI生成的语音
assets/audio/
# 比如未来生成的日记或文档
assets/documents/
# 我们规划的日志文件夹
logs/

# 忽略编辑器和操作系统的配置文件
.vscode/
.idea/
.DS_Store

# 忽略任何可能的敏感信息文件（非常重要！）
# 建议未来把API Key等信息写在 secrets.py 中，并把这个文件加入忽略列表
config/secrets.py

# 忽略临时的依赖文件
temp_requirements.txt

# 忽略我们下载的 .whl 安装包
*.whl

# 新的安装脚本相关忽略
# 本地Miniconda安装
miniconda3/
# 项目本地Python环境
env/
# 安装包下载
Miniconda3-Installer.exe
# 自动生成的激活脚本
activate.bat

# 内存和缓存数据
*.db
*.db.backup_*
*.db.id_migration_backup_*
data/
assets/memory.db*
assets/test_*.db
assets/audio/

# 模型和AI相关
models/
*.bin
*.meta
*.safetensors
*.onnx
*.npy

# 音频和多媒体
data/tts/
*.wav
*.mp3
*.flac
*.m4a

# 临时文件和日志
temp/
logs/
*.log
*.tmp
*.temp

# 其他运行时目录
whl/
old_system/
llama/models/
models/*.bin
models/*.safetensors
cache/
summer/


# 新的配置文件保护
config/api_keys.py
config/secrets.py
config/local_settings.py

#开发计划
development_plan/

.claude/
test_result/