---
description: 
globs: 
alwaysApply: false
---
# 音频系统架构指南

## 音频系统核心
音频系统位于 `core/audio/` 目录，负责语音输入输出和键盘控制。

## 核心组件

### 音频输入
- [core/audio/input.py](mdc:core/audio/input.py) - 语音输入处理，支持Whisper语音识别

### 音频输出  
- [core/audio/output.py](mdc:core/audio/output.py) - 语音输出处理，支持TTS文本转语音

### 系统音频
- [core/audio/system.py](mdc:core/audio/system.py) - 系统音频管理和配置

### 键盘控制
- [core/audio/keyboard_control.py](mdc:core/audio/keyboard_control.py) - 键盘快捷键控制音频功能

## 功能特性
1. **语音识别** - 基于Whisper的实时语音转文本
2. **语音合成** - TTS文本转语音输出
3. **热键控制** - 键盘快捷键控制录音和播放
4. **音频设备管理** - 自动检测和配置音频设备

## 音频资源
- 音频文件存储在 `assets/audio/` 目录
- 支持多种音频格式
- 自动音频质量优化

## 配置选项
- 音频设备选择和配置
- 语音识别语言设置
- TTS语音和语速配置
- 热键自定义设置



