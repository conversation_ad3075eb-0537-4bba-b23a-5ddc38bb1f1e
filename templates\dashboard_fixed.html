<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estia 记忆监控仪表板</title>
    
    <!-- 引入Chart.js和其他依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.0.0/dist/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/wordcloud@1.2.2/src/wordcloud2.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2em;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #48bb78; }
        .status-idle { background: #a0aec0; }
        .status-error { background: #f56565; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child { border-bottom: none; }
        
        .metric-value {
            font-weight: bold;
            color: #2d3748;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        
        .wordcloud-container {
            height: 300px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .session-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .session-item {
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .session-id { 
            font-family: monospace;
            color: #6b7280;
            font-size: 0.9em;
        }
        
        .session-duration {
            color: #059669;
            font-weight: bold;
        }
        
        .session-query {
            color: #374151;
            font-style: italic;
            margin-bottom: 5px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(66,153,225,0.4);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        .test-btn {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: #48bb78;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(72,187,120,0.4);
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #38a169;
            transform: translateY(-2px);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
        }
        
        .notification.success {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            color: #2f855a;
        }
        
        .notification.error {
            background: #fef5e7;
            border-left: 4px solid #f56565;
            color: #c53030;
        }
        
        .notification.info {
            background: #ebf8ff;
            border-left: 4px solid #4299e1;
            color: #2b6cb0;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
            .header h1 { font-size: 2em; }
            .container { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Estia 记忆监控仪表板</h1>
        <p class="subtitle">13步记忆处理流程实时监控与分析</p>
        <div id="data-source-indicator" style="margin-top: 10px; padding: 5px 15px; border-radius: 20px; display: inline-block; font-size: 0.9em;">
            <span id="data-source-text">检测数据源中...</span>
        </div>
    </div>
    
    <div class="container">
        <div class="grid">
            <!-- 实时状态卡片 -->
            <div class="card">
                <h3>📊 实时状态</h3>
                <div id="status-content">
                    <div class="metric">
                        <span>系统状态</span>
                        <span class="metric-value">
                            <span class="status-indicator status-idle"></span>
                            <span id="system-status">加载中...</span>
                        </span>
                    </div>
                    <div class="metric">
                        <span>当前会话</span>
                        <span class="metric-value" id="current-session">无</span>
                    </div>
                    <div class="metric">
                        <span>运行时间</span>
                        <span class="metric-value" id="runtime">0s</span>
                    </div>
                    <div class="metric">
                        <span>进度</span>
                        <span class="metric-value" id="progress">0%</span>
                    </div>
                </div>
            </div>
            
            <!-- 性能统计卡片 -->
            <div class="card">
                <h3>📈 性能统计</h3>
                <div id="performance-content">
                    <div class="metric">
                        <span>总会话数</span>
                        <span class="metric-value" id="total-sessions">0</span>
                    </div>
                    <div class="metric">
                        <span>平均耗时</span>
                        <span class="metric-value" id="avg-duration">0s</span>
                    </div>
                    <div class="metric">
                        <span>成功率</span>
                        <span class="metric-value" id="success-rate">0%</span>
                    </div>
                    <div class="metric">
                        <span>最慢步骤</span>
                        <span class="metric-value" id="slowest-step">无</span>
                    </div>
                </div>
            </div>
            
            <!-- 关键词云 -->
            <div class="card">
                <h3>☁️ 关键词云</h3>
                <div class="wordcloud-container" id="wordcloud"></div>
                <div id="keyword-stats" style="margin-top: 15px;">
                    <div class="metric">
                        <span>热门词汇</span>
                        <span class="metric-value" id="top-keywords">加载中...</span>
                    </div>
                </div>
            </div>
            
            <!-- 性能趋势图 -->
            <div class="card">
                <h3>📊 性能趋势</h3>
                <div class="chart-container">
                    <canvas id="performance-chart"></canvas>
                </div>
            </div>
            
            <!-- 记忆分析 -->
            <div class="card">
                <h3>🧠 记忆分析</h3>
                <div id="memory-analysis">
                    <div class="metric">
                        <span>平均相似度</span>
                        <span class="metric-value" id="avg-similarity">0</span>
                    </div>
                    <div class="metric">
                        <span>检索次数</span>
                        <span class="metric-value" id="retrieval-count">0</span>
                    </div>
                    <div class="metric">
                        <span>关联拓展</span>
                        <span class="metric-value" id="associations-count">0</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="memory-chart"></canvas>
                </div>
            </div>
            
            <!-- 最近会话 -->
            <div class="card">
                <h3>💬 最近会话</h3>
                <div class="session-list" id="session-list">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="manualRefresh()">
        🔄 刷新数据
    </button>
    
    <button class="test-btn" onclick="loadTestData()">
        🧪 加载测试数据
    </button>

    <script>
        // 全局变量
        let performanceChart, memoryChart;
        let dataCache = new Map();
        let isUpdating = false;

        // WebSocket连接
        const socket = io();

        // 通知系统
        function showNotification(message, type = 'info', duration = 5000) {
            // 避免重复通知
            const existingNotifications = document.querySelectorAll('.notification');
            for (const existing of existingNotifications) {
                if (existing.textContent.includes(message.substring(0, 20))) {
                    return;
                }
            }

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const icons = {
                'error': '⚠️',
                'info': 'ℹ️',
                'success': '✅'
            };

            notification.innerHTML = `
                <strong>${icons[type] || 'ℹ️'} ${type === 'error' ? '错误' : type === 'success' ? '成功' : '信息'}</strong><br>
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer; color: inherit;">&times;</button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        }

        function showErrorNotification(message) {
            showNotification(message, 'error');
        }

        function showSuccessNotification(message) {
            showNotification(message, 'success', 3000);
        }

        function showInfoNotification(message) {
            showNotification(message, 'info', 4000);
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }

        // 初始化图表
        function initCharts() {
            // 性能趋势图
            const perfCtx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '会话耗时 (秒)',
                        data: [],
                        borderColor: '#4299e1',
                        backgroundColor: 'rgba(66,153,225,0.1)',
                        tension: 0.4,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: { duration: 300 },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { maxTicksLimit: 6 }
                        },
                        x: {
                            ticks: { maxTicksLimit: 10 }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });

            // 记忆分析图
            const memCtx = document.getElementById('memory-chart').getContext('2d');
            memoryChart = new Chart(memCtx, {
                type: 'doughnut',
                data: {
                    labels: ['高相似度', '中相似度', '低相似度'],
                    datasets: [{
                        data: [0, 0, 0],
                        backgroundColor: ['#48bb78', '#ed8936', '#f56565'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: { duration: 300 },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 10
                            }
                        }
                    }
                }
            });
        }

        // 更新状态显示
        function updateStatus(data) {
            if (!data || !data.status) return;

            const status = data.status;
            const summary = data.summary || {};

            // 更新系统状态
            const statusEl = document.getElementById('system-status');
            const indicatorEl = statusEl?.previousElementSibling;

            if (statusEl && indicatorEl) {
                if (status.status === 'running') {
                    statusEl.textContent = '运行中';
                    indicatorEl.className = 'status-indicator status-running';
                } else {
                    statusEl.textContent = '空闲';
                    indicatorEl.className = 'status-indicator status-idle';
                }
            }

            // 批量更新DOM
            const elementUpdates = [
                ['current-session', status.session_id || '无'],
                ['runtime', (status.running_time || 0).toFixed(2) + 's'],
                ['progress', (status.progress_percentage || 0).toFixed(1) + '%']
            ];

            if (summary.total_sessions !== undefined) {
                elementUpdates.push(
                    ['total-sessions', summary.total_sessions],
                    ['avg-duration', (summary.average_duration || 0).toFixed(3) + 's'],
                    ['success-rate', ((summary.success_rate || 0) * 100).toFixed(1) + '%']
                );

                const slowest = summary.slowest_step;
                const slowestText = slowest && slowest.step ?
                    `${slowest.step.split('_').pop()} (${(slowest.avg_duration || 0).toFixed(3)}s)` : '无';
                elementUpdates.push(['slowest-step', slowestText]);
            }

            // 执行DOM更新
            requestAnimationFrame(() => {
                elementUpdates.forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element && element.textContent !== String(value)) {
                        element.textContent = value;
                    }
                });
            });
        }

        // 更新关键词云
        const updateWordCloud = debounce(function(keywords) {
            const container = document.getElementById('wordcloud');
            if (!container) return;

            // 检查数据是否变化
            const cacheKey = 'wordcloud_data';
            const cachedData = dataCache.get(cacheKey);
            const currentData = JSON.stringify(keywords);

            if (cachedData === currentData) return;
            dataCache.set(cacheKey, currentData);

            requestAnimationFrame(() => {
                if (keywords && keywords.top_keywords && keywords.top_keywords.length > 0) {
                    const wordList = keywords.top_keywords.map(item => [
                        item.word, Math.max(12, Math.min(40, item.count * 8))
                    ]);

                    container.innerHTML = '';

                    try {
                        WordCloud(container, {
                            list: wordList,
                            gridSize: 10,
                            weightFactor: 1.5,
                            fontFamily: 'Microsoft YaHei, sans-serif',
                            color: function() {
                                const colors = ['#4299e1', '#48bb78', '#ed8936', '#9f7aea', '#f56565'];
                                return colors[Math.floor(Math.random() * colors.length)];
                            },
                            rotateRatio: 0.1,
                            backgroundColor: '#f8f9fa',
                            minSize: 12,
                            drawOutOfBound: false
                        });

                        const topWords = keywords.top_keywords.slice(0, 3)
                            .map(item => item.word).join(', ');
                        const topKeywordsEl = document.getElementById('top-keywords');
                        if (topKeywordsEl) {
                            topKeywordsEl.textContent = topWords;
                        }
                    } catch (error) {
                        console.warn('词云生成失败:', error);
                        container.innerHTML = '<p style="text-align: center; margin-top: 100px; color: #f56565;">词云生成失败</p>';
                    }
                } else {
                    container.innerHTML = '<p style="text-align: center; margin-top: 100px; color: #a0aec0;">暂无关键词数据</p>';
                    const topKeywordsEl = document.getElementById('top-keywords');
                    if (topKeywordsEl) {
                        topKeywordsEl.textContent = '暂无数据';
                    }
                }
            });
        }, 2000);

        // 更新会话列表
        const updateSessionList = throttle(function(sessions) {
            const container = document.getElementById('session-list');
            if (!container) return;

            // 检查数据是否变化
            const cacheKey = 'session_list_data';
            const cachedData = dataCache.get(cacheKey);
            const currentData = JSON.stringify(sessions);

            if (cachedData === currentData) return;
            dataCache.set(cacheKey, currentData);

            requestAnimationFrame(() => {
                if (!sessions || sessions.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #a0aec0; padding: 20px;">暂无会话数据</p>';
                    return;
                }

                const fragment = document.createDocumentFragment();

                sessions.forEach(session => {
                    const sessionDiv = document.createElement('div');
                    sessionDiv.className = 'session-item';

                    const sessionId = session.session_id || 'unknown';
                    const duration = (session.duration || 0).toFixed(3);
                    const userInput = (session.user_input || '').substring(0, 100);
                    const successCount = session.success_count || 0;
                    const failedCount = session.failed_count || 0;

                    sessionDiv.innerHTML = `
                        <div class="session-header">
                            <span class="session-id">${sessionId}</span>
                            <span class="session-duration">${duration}s</span>
                        </div>
                        <div class="session-query">"${userInput}"</div>
                        <div style="font-size: 0.9em; color: #6b7280;">
                            成功: ${successCount} | 失败: ${failedCount}
                        </div>
                    `;

                    fragment.appendChild(sessionDiv);
                });

                container.innerHTML = '';
                container.appendChild(fragment);
            });
        }, 1500);

        // 更新性能图表
        const updatePerformanceChart = throttle(function(labels, data) {
            if (!performanceChart || !labels || !data) return;

            const currentData = performanceChart.data.datasets[0].data;
            if (JSON.stringify(currentData) === JSON.stringify(data)) return;

            performanceChart.data.labels = labels;
            performanceChart.data.datasets[0].data = data;
            performanceChart.update('none');
        }, 1000);

        // 更新记忆图表
        const updateMemoryChart = throttle(function(data) {
            if (!memoryChart || !data) return;

            const currentData = memoryChart.data.datasets[0].data;
            if (JSON.stringify(currentData) === JSON.stringify(data)) return;

            memoryChart.data.datasets[0].data = data;
            memoryChart.update('none');
        }, 1000);

        // 更新记忆分析
        function updateMemoryAnalysis(memory) {
            if (!memory) return;

            const updates = [
                ['avg-similarity', (memory.average_similarity || 0).toFixed(3)],
                ['retrieval-count', memory.total_retrievals || 0],
                ['associations-count', memory.memory_usage_stats?.associations || 0]
            ];

            requestAnimationFrame(() => {
                updates.forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element && element.textContent !== String(value)) {
                        element.textContent = value;
                    }
                });
            });

            if (memory.similarity_distribution) {
                const dist = memory.similarity_distribution;
                const chartData = [
                    dist['高 (>0.8)'] || 0,
                    dist['中 (0.6-0.8)'] || 0,
                    dist['低 (<0.6)'] || 0
                ];
                updateMemoryChart(chartData);
            }
        }

        // 批量数据刷新函数
        async function refreshAllData() {
            if (isUpdating) {
                console.log('正在更新中，跳过本次刷新');
                return;
            }

            isUpdating = true;
            const startTime = performance.now();

            try {
                const response = await fetch('/api/dashboard_data');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 更新数据源指示器
                updateDataSourceIndicator(data);

                if (data.error && !data.has_data) {
                    if (data.error.includes('暂无数据')) {
                        showInfoNotification('暂无监控数据，可以点击"加载测试数据"查看界面效果');
                    } else {
                        showErrorNotification(data.error);
                    }
                }

                // 批量更新所有组件
                const updatePromises = [];

                if (data.status) {
                    updatePromises.push(Promise.resolve(updateStatus(data.status)));
                }

                if (data.keywords) {
                    updatePromises.push(Promise.resolve(updateWordCloud(data.keywords)));
                }

                if (data.sessions && data.sessions.sessions) {
                    updatePromises.push(Promise.resolve(updateSessionList(data.sessions.sessions)));

                    if (data.sessions.sessions.length > 0) {
                        const sessions = data.sessions.sessions;
                        const labels = sessions.map((_, i) => `会话${i+1}`);
                        const durations = sessions.map(s => s.duration || 0);

                        updatePromises.push(Promise.resolve(
                            updatePerformanceChart(labels.slice(-10), durations.slice(-10))
                        ));
                    }
                }

                if (data.memory) {
                    updatePromises.push(Promise.resolve(updateMemoryAnalysis(data.memory)));
                }

                await Promise.all(updatePromises);

                const endTime = performance.now();
                console.log(`数据刷新完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

            } catch (error) {
                console.error('批量数据刷新失败:', error);
                showErrorNotification('数据刷新失败: ' + error.message);
            } finally {
                isUpdating = false;
            }
        }

        // 全局手动刷新函数
        window.manualRefresh = async function() {
            try {
                showInfoNotification('正在刷新数据...');
                await refreshAllData();
                showSuccessNotification('数据刷新成功！');
            } catch (error) {
                console.error('手动刷新失败:', error);
                showErrorNotification('数据刷新失败: ' + error.message);
            }
        };

        // 全局测试数据加载函数
        window.loadTestData = async function() {
            try {
                showInfoNotification('正在加载测试数据...');

                const response = await fetch('/api/generate_test_data');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                // 更新所有组件
                if (data.status) {
                    updateStatus(data.status);
                }

                if (data.keywords) {
                    updateWordCloud(data.keywords);
                }

                if (data.sessions && data.sessions.sessions) {
                    updateSessionList(data.sessions.sessions);

                    const sessions = data.sessions.sessions;
                    const labels = sessions.map((_, i) => `会话${i+1}`);
                    const durations = sessions.map(s => s.duration || 0);
                    updatePerformanceChart(labels, durations);
                }

                if (data.memory) {
                    updateMemoryAnalysis(data.memory);
                }

                showSuccessNotification('测试数据加载成功！现在可以看到界面效果了。');

            } catch (error) {
                console.error('加载测试数据失败:', error);
                showErrorNotification('测试数据加载失败: ' + error.message);
            }
        };

        // WebSocket事件处理
        socket.on('connect', function() {
            console.log('✅ 连接到监控服务器');
            socket.emit('start_monitoring');
            showSuccessNotification('实时监控连接已建立');
        });

        socket.on('disconnect', function() {
            console.log('❌ 与监控服务器断开连接');
            showErrorNotification('实时监控连接已断开');
        });

        socket.on('status_update', function(data) {
            try {
                updateStatus(data);
            } catch (error) {
                console.error('状态更新失败:', error);
            }
        });

        socket.on('monitoring_error', function(data) {
            console.error('监控错误:', data);
            showErrorNotification('监控服务出现错误: ' + data.error);
        });

        socket.on('connect_error', function(error) {
            console.error('WebSocket连接错误:', error);
            showErrorNotification('无法连接到监控服务器');
        });

        // 更新数据源指示器
        function updateDataSourceIndicator(data) {
            const indicator = document.getElementById('data-source-indicator');
            const textElement = document.getElementById('data-source-text');

            if (!indicator || !textElement) return;

            let text = '未知数据源';
            let bgColor = '#a0aec0';
            let textColor = '#2d3748';

            if (data.live_mode) {
                text = '🔴 实时数据 (连接到运行中的Estia系统)';
                bgColor = '#c6f6d5';
                textColor = '#2f855a';
            } else if (data.test_mode) {
                text = '🧪 测试数据 (模拟数据)';
                bgColor = '#feebc8';
                textColor = '#c05621';
            } else if (data.data_source === 'mock_monitor') {
                text = '⚠️ 模拟监控 (Estia系统未运行)';
                bgColor = '#fed7d7';
                textColor = '#c53030';
            }

            indicator.style.backgroundColor = bgColor;
            indicator.style.color = textColor;
            textElement.textContent = text;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化仪表板...');

            // 初始化图表
            initCharts();

            // 更新数据源指示器初始状态
            const indicator = document.getElementById('data-source-indicator');
            if (indicator) {
                indicator.style.backgroundColor = '#e2e8f0';
                indicator.style.color = '#4a5568';
            }

            // 首次加载数据
            refreshAllData().then(() => {
                console.log('初始数据加载完成');
            }).catch(error => {
                console.error('初始数据加载失败:', error);
                showErrorNotification('初始数据加载失败');
            });

            // 定期刷新数据
            setInterval(() => {
                if (!isUpdating) {
                    refreshAllData().catch(console.error);
                }
            }, 8000); // 每8秒刷新一次

            // 页面可见性变化监听
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    // 页面变为可见时立即刷新
                    refreshAllData().catch(console.error);
                }
            });
        });
    </script>
</body>
</html>
