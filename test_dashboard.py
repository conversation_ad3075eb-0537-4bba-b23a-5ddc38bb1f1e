#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web监控仪表板 - 简化版
"""

import sys
import os
from flask import Flask, render_template, jsonify
from datetime import datetime
import random

# 确保路径正确
sys.path.append('.')

app = Flask(__name__, template_folder='templates')

@app.route('/')
def dashboard():
    """主仪表板页面"""
    return render_template('dashboard.html')

@app.route('/api/dashboard_data')
def get_dashboard_data():
    """批量获取仪表板数据（测试版）"""
    try:
        # 生成测试数据
        test_sessions = []
        for i in range(5):
            test_sessions.append({
                'session_id': f'test_session_{i+1}',
                'start_time': datetime.now().isoformat(),
                'duration': random.uniform(0.5, 3.0),
                'success_count': random.randint(8, 14),
                'failed_count': random.randint(0, 2),
                'user_input': f'测试查询 {i+1}',
                'ai_response': f'测试回复 {i+1}...'
            })
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'has_data': True,
            'test_mode': True,
            'status': {
                'status': {
                    'status': 'idle',
                    'session_id': None,
                    'running_time': 10.5,
                    'progress_percentage': 0
                },
                'summary': {
                    'total_sessions': 5,
                    'average_duration': 1.5,
                    'success_rate': 0.92,
                    'slowest_step': {
                        'step': 'step_5_faiss_search',
                        'avg_duration': 0.234
                    }
                }
            },
            'keywords': {
                'top_keywords': [
                    {'word': '测试', 'count': 15, 'frequency': 0.8},
                    {'word': '查询', 'count': 12, 'frequency': 0.6},
                    {'word': '数据', 'count': 10, 'frequency': 0.5}
                ],
                'total_unique_keywords': 25,
                'keyword_distribution': {'测试': 15, '查询': 12, '数据': 10}
            },
            'sessions': {
                'sessions': test_sessions,
                'total': len(test_sessions)
            },
            'memory': {
                'average_similarity': 0.75,
                'memory_usage_stats': {'retrieved': 45, 'associations': 23},
                'total_retrievals': 45,
                'similarity_distribution': {
                    '高 (>0.8)': 15,
                    '中 (0.6-0.8)': 20,
                    '低 (<0.6)': 10
                }
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'error': f'获取数据失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/status')
def get_status():
    """获取状态"""
    return jsonify({
        'status': {
            'status': 'idle',
            'session_id': None,
            'running_time': 10.5,
            'progress_percentage': 0
        },
        'summary': {
            'total_sessions': 5,
            'average_duration': 1.5,
            'success_rate': 0.92,
            'slowest_step': None
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🧪 启动测试版监控仪表板...")
    print("📊 访问地址: http://localhost:5001")
    print("🔄 这是简化的测试版本")
    print("⚠️  按 Ctrl+C 停止服务器")
    print("="*50)
    
    try:
        app.run(host='0.0.0.0', port=5001, debug=True)
    except KeyboardInterrupt:
        print("\n👋 测试仪表板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
